#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.ui.shipments.new_shipment_window import NewShipmentWindow

def test_excel_import():
    """اختبار وظيفة استيراد Excel"""
    
    print("🚀 بدء اختبار وظيفة استيراد Excel...")
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء نافذة الشحنة الجديدة
    window = NewShipmentWindow()
    window.show()
    
    print("✅ تم فتح نافذة الشحنة الجديدة")
    print("🔍 تحقق من وجود:")
    print("   • زر 'استيراد Excel' في شريط الأزرار")
    print("   • الزر يحمل أيقونة 📊 ولون بنفسجي")
    print("   • عند النقر على الزر يجب أن تظهر نافذة اختيار الملف")
    print("")
    print("📋 خطوات الاختبار:")
    print("   1. انقر على زر 'استيراد Excel'")
    print("   2. اختر الملف: sample_shipment_data.xlsx")
    print("   3. في نافذة التطابق، اختر الأعمدة المناسبة:")
    print("      - سعر الصرف ← سعر_الصرف")
    print("      - قيمة البضاعة ← قيمة_البضاعة")
    print("      - تكلفة الشحن ← تكلفة_الشحن")
    print("      - تكلفة التأمين ← تكلفة_التأمين")
    print("      - رسوم الجمارك ← رسوم_الجمارك")
    print("      - رسوم أخرى ← رسوم_أخرى")
    print("      - المبلغ المدفوع ← المبلغ_المدفوع")
    print("      - شركة الشحن ← شركة_الشحن")
    print("      - ميناء الوصول ← ميناء_الوصول")
    print("      - رقم DHL ← رقم_DHL")
    print("      - تاريخ الوصول المتوقع ← تاريخ_الوصول_المتوقع")
    print("      - رقم الحاوية ← رقم_الحاوية")
    print("   4. انقر على 'استيراد البيانات'")
    print("   5. تحقق من ملء الحقول في التبويبات المختلفة")
    print("")
    print("📁 ملف Excel التجريبي متوفر: sample_shipment_data.xlsx")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_excel_import()
