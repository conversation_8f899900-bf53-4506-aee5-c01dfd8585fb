#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة
هذه النافذة تحل محل النافذة الأصلية المعقدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                               QTabWidget, QWidget, QGroupBox, QFormLayout,
                               QLineEdit, QComboBox, QTextEdit, QDateEdit,
                               QSpinBox, QTableWidget, QMessageBox, QLabel,
                               QFrame, QToolBar, QDialogButtonBox, QApplication,
                               Q<PERSON><PERSON>er<PERSON>iew, QAbstract<PERSON>tem<PERSON><PERSON>w, QGridLayout,
                               QDoubleSpinBox, QTableWidgetItem)
from PySide6.QtCore import Signal, QDate, Qt, QSize, QLocale
from PySide6.QtGui import QFont, QAction, QKeySequence, QShortcut

# استيراد النماذج المطلوبة لحفظ البيانات
from ...database.database_manager import DatabaseManager
from ...database.models import Shipment, ShipmentItem, Container, ShipmentDocument
from ...utils.formatters import format_date, format_datetime, format_number, format_currency

# استيراد نوافذ الحوار
try:
    from ..dialogs.supplier_search_dialog import SupplierSearchDialog
    from ..dialogs.item_search_dialog import ItemSearchDialog
    from ..dialogs.container_dialog import ContainerDialog
    from ..dialogs.item_price_dialog import ItemPriceDialog
    from ..dialogs.excel_import_dialog import ExcelImportDialog
except ImportError as e:
    print(f"تحذير: فشل في استيراد بعض نوافذ الحوار: {e}")

class NewShipmentWindow(QDialog):
    """نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة"""
    
    shipment_saved = Signal(int)
    
    def __init__(self, parent=None, shipment_id=None):
        super().__init__(parent)
        self.current_shipment_id = shipment_id  # معرف الشحنة للتعديل
        self.db_manager = DatabaseManager()
        self.selected_supplier_id = None  # معرف المورد المختار
        self.is_edit_mode = shipment_id is not None  # وضع التعديل
        self.setup_ui()
        self.setup_connections()

        # إذا كان في وضع التعديل، تحميل بيانات الشحنة
        if self.is_edit_mode:
            self.load_shipment_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        if self.is_edit_mode:
            self.setWindowTitle("✏️ تعديل الشحنة - مع أزرار التحكم")
        else:
            self.setWindowTitle("🚢 شحنة جديدة - مع أزرار التحكم")
        self.setModal(True)
        self.resize(1440, 990)  # الحجم المطلوب
        self.setMinimumSize(1000, 500)  # حد أدنى للحجم

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)  # تقليل المسافات
        main_layout.setContentsMargins(15, 15, 15, 15)  # تقليل الهوامش
        
        # عنوان النافذة
        self.create_title_header(main_layout)
        
        # شريط أزرار التحكم
        self.create_control_toolbar(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        # أزرار الحفظ والإلغاء
        self.create_dialog_buttons(main_layout)
        
    def create_title_header(self, main_layout):
        """إنشاء عنوان النافذة"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 8px;
                padding: 8px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🚢 نظام إدارة الشحنات - شحنة جديدة")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
            }
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        main_layout.addWidget(title_frame)
        
    def create_control_toolbar(self, main_layout):
        """إنشاء شريط أزرار التحكم"""
        # إطار الأزرار
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 8px;
                margin: 3px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setSpacing(15)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)

        # زر إضافة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setFixedSize(120, 40)
        self.new_button.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setFixedSize(120, 40)
        self.save_button.setStyleSheet(self.get_button_style("#27ae60", "#229954"))

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setFixedSize(120, 40)
        self.edit_button.setStyleSheet(self.get_button_style("#f39c12", "#e67e22"))
        self.edit_button.setEnabled(False)

        # زر استيراد من Excel
        self.import_excel_button = QPushButton("📊 استيراد Excel")
        self.import_excel_button.setFixedSize(140, 40)
        self.import_excel_button.setStyleSheet(self.get_button_style("#9b59b6", "#8e44ad"))

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setFixedSize(120, 40)
        self.exit_button.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))

        # ترتيب الأزرار
        toolbar_layout.addWidget(self.new_button)
        toolbar_layout.addWidget(self.save_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.import_excel_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.exit_button)
        
        main_layout.addWidget(toolbar_frame)
        
    def get_button_style(self, color1, color2):
        """الحصول على نمط الزر"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                font-weight: bold;
                font-size: 12px;
                border: 2px solid {color2};
                border-radius: 8px;
            }}
            QPushButton:hover {{
                background: {color2};
                border: 2px solid {color1};
            }}
            QPushButton:pressed {{
                background: {color1};
            }}
            QPushButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
                border: 2px solid #95a5a6;
            }}
        """
        
    def create_tabs(self, main_layout):
        """إنشاء التبويبات"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                padding: 5px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 15px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # إنشاء التبويبات
        self.create_basic_tab()
        self.create_items_tab()
        self.create_financial_tab()
        self.create_shipping_tab()
        self.create_containers_tab()
        self.create_documents_tab()
        
        main_layout.addWidget(self.tab_widget)
        
    def create_basic_tab(self):
        """إنشاء التبويب الأساسي"""
        basic_tab = QWidget()
        self.tab_widget.addTab(basic_tab, "📋 البيانات الأساسية")
        
        layout = QVBoxLayout(basic_tab)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #2c3e50;
                background-color: white;
            }
        """)
        
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(8)

        # الصف الأول: التاريخ + رقم الشحنة + المورد
        basic_layout.addWidget(QLabel("التاريخ:"), 0, 0)
        self.shipment_date_edit = QDateEdit()
        self.shipment_date_edit.setDate(QDate.currentDate())
        self.shipment_date_edit.setCalendarPopup(True)
        self.shipment_date_edit.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للتاريخ
        self.shipment_date_edit.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        self.shipment_date_edit.setDisplayFormat("dd/MM/yyyy")
        basic_layout.addWidget(self.shipment_date_edit, 0, 1)

        basic_layout.addWidget(QLabel("رقم الشحنة:"), 0, 2)

        # تخطيط رقم الشحنة مع زر التوليد
        shipment_number_layout = QHBoxLayout()
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setReadOnly(False)  # السماح بالتعديل
        unique_number = self.generate_unique_shipment_number()
        self.shipment_number_edit.setText(unique_number)
        self.shipment_number_edit.setStyleSheet(self.get_input_style())
        shipment_number_layout.addWidget(self.shipment_number_edit)

        # زر توليد رقم جديد
        generate_number_btn = QPushButton("🔄")
        generate_number_btn.setToolTip("توليد رقم شحنة جديد")
        generate_number_btn.setFixedSize(30, 30)
        generate_number_btn.clicked.connect(self.generate_new_shipment_number)
        generate_number_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        shipment_number_layout.addWidget(generate_number_btn)

        basic_layout.addLayout(shipment_number_layout, 0, 3)

        basic_layout.addWidget(QLabel("المورد:"), 0, 4)
        supplier_layout = QHBoxLayout()
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("اكتب اسم المورد أو اضغط F9 للبحث")
        self.supplier_edit.setStyleSheet(self.get_input_style())
        self.supplier_search_button = QPushButton("F9")
        self.supplier_search_button.setMaximumWidth(35)
        self.supplier_search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        supplier_layout.addWidget(self.supplier_edit)
        supplier_layout.addWidget(self.supplier_search_button)
        supplier_widget = QWidget()
        supplier_widget.setLayout(supplier_layout)
        basic_layout.addWidget(supplier_widget, 0, 5)

        # الصف الثاني: فاتورة المورد + حالة الشحنة
        basic_layout.addWidget(QLabel("فاتورة المورد:"), 1, 0)
        self.supplier_invoice_edit = QLineEdit()
        self.supplier_invoice_edit.setPlaceholderText("رقم فاتورة المورد...")
        self.supplier_invoice_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.supplier_invoice_edit, 1, 1)

        basic_layout.addWidget(QLabel("حالة الشحنة:"), 1, 2)
        self.shipment_status_combo = QComboBox()
        self.shipment_status_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق",
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        self.shipment_status_combo.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.shipment_status_combo, 1, 3)

        # الصف الثالث: حالة الإفراج + رقم التتبع
        basic_layout.addWidget(QLabel("حالة الإفراج:"), 2, 0)
        self.clearance_status_combo = QComboBox()
        self.clearance_status_combo.addItems(["بدون الافراج", "مع الافراج"])
        self.clearance_status_combo.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.clearance_status_combo, 2, 1)

        basic_layout.addWidget(QLabel("رقم التتبع:"), 2, 2)
        self.tracking_number_edit = QLineEdit()
        self.tracking_number_edit.setPlaceholderText("رقم التتبع...")
        self.tracking_number_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.tracking_number_edit, 2, 3)

        # الصف الرابع: بوليصة الشحن + ملاحظات
        basic_layout.addWidget(QLabel("بوليصة الشحن:"), 3, 0)
        self.bill_of_lading_edit = QLineEdit()
        self.bill_of_lading_edit.setPlaceholderText("رقم بوليصة الشحن...")
        self.bill_of_lading_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.bill_of_lading_edit, 3, 1)

        basic_layout.addWidget(QLabel("ملاحظات:"), 3, 2)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(50)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات...")
        self.notes_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.notes_edit, 3, 3)

        layout.addWidget(basic_group)
        layout.addStretch()
        
    def get_input_style(self, readonly=False):
        """الحصول على نمط الحقول"""
        bg_color = "#f8f9fa" if readonly else "white"
        return f"""
            QLineEdit, QComboBox, QTextEdit, QDateEdit {{
                background-color: {bg_color};
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus, QDateEdit:focus {{
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: 2px solid #3498db;
                width: 10px;
                height: 10px;
            }}
        """
        
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        items_tab = QWidget()
        self.tab_widget.addTab(items_tab, "📦 الأصناف")

        layout = QVBoxLayout(items_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # أزرار إدارة الأصناف
        buttons_layout = QHBoxLayout()

        self.add_item_button = QPushButton("إضافة صنف (F9)")
        self.add_item_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.remove_item_button = QPushButton("حذف صنف")
        self.remove_item_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.remove_item_button.setEnabled(False)

        self.edit_item_button = QPushButton("تعديل صنف")
        self.edit_item_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        self.edit_item_button.setEnabled(False)

        buttons_layout.addWidget(self.add_item_button)
        buttons_layout.addWidget(self.remove_item_button)
        buttons_layout.addWidget(self.edit_item_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة",
            "السعر الإجمالي", "الوزن", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # كود الصنف
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الصنف
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # سعر الوحدة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # السعر الإجمالي
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الوزن
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # ملاحظات

        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.items_table)

        # إجمالي الأصناف
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_items_label = QLabel("إجمالي الأصناف: 0")
        self.total_items_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        self.total_value_label = QLabel("إجمالي القيمة: 0.00")
        self.total_value_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_items_label)
        totals_layout.addWidget(self.total_value_label)

        layout.addLayout(totals_layout)
        
    def create_financial_tab(self):
        """إنشاء تبويب المالية"""
        financial_tab = QWidget()
        self.tab_widget.addTab(financial_tab, "💰 البيانات المالية")

        layout = QVBoxLayout(financial_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # مجموعة البيانات المالية
        financial_group = QGroupBox("البيانات المالية")
        financial_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #f39c12;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #f39c12;
                background-color: white;
            }
        """)
        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(12)
        financial_layout.setHorizontalSpacing(8)
        financial_layout.setVerticalSpacing(12)

        # ضبط عرض الأعمدة لجعل التسميات أقرب للحقول
        financial_layout.setColumnMinimumWidth(0, 120)  # عمود التسميات الأول
        financial_layout.setColumnMinimumWidth(1, 150)  # عمود الحقول الأول
        financial_layout.setColumnMinimumWidth(2, 120)  # عمود التسميات الثاني
        financial_layout.setColumnMinimumWidth(3, 150)  # عمود الحقول الثاني
        financial_layout.setColumnMinimumWidth(4, 140)  # عمود التسميات الثالث
        financial_layout.setColumnMinimumWidth(5, 150)  # عمود الحقول الثالث
        financial_layout.setColumnStretch(0, 0)
        financial_layout.setColumnStretch(1, 1)
        financial_layout.setColumnStretch(2, 0)
        financial_layout.setColumnStretch(3, 1)
        financial_layout.setColumnStretch(4, 0)
        financial_layout.setColumnStretch(5, 1)

        # الصف الأول: العملة + سعر الصرف + قيمة البضاعة
        currency_label = QLabel("العملة:")
        currency_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        currency_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(currency_label, 0, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["USD", "EUR", "SAR", "AED", "KWD", "QAR", "BHD"])
        self.currency_combo.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.currency_combo, 0, 1)

        exchange_rate_label = QLabel("سعر الصرف:")
        exchange_rate_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        exchange_rate_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(exchange_rate_label, 0, 2)
        self.exchange_rate_edit = QLineEdit()
        self.exchange_rate_edit.setPlaceholderText("1.0000")
        self.exchange_rate_edit.setText("1.0000")
        self.exchange_rate_edit.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.exchange_rate_edit, 0, 3)

        goods_value_label = QLabel("قيمة البضاعة:")
        goods_value_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        goods_value_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(goods_value_label, 0, 4)
        self.goods_value_edit = QLineEdit()
        self.goods_value_edit.setPlaceholderText("0.00")
        self.goods_value_edit.setText("0.00")
        self.goods_value_edit.setReadOnly(True)
        self.goods_value_edit.setStyleSheet(self.get_input_style(readonly=True))
        financial_layout.addWidget(self.goods_value_edit, 0, 5)

        # الصف الثاني: تكلفة الشحن + التأمين + رسوم الجمارك
        shipping_cost_label = QLabel("تكلفة الشحن:")
        shipping_cost_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        shipping_cost_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(shipping_cost_label, 1, 0)
        self.shipping_cost_edit = QLineEdit()
        self.shipping_cost_edit.setPlaceholderText("0.00")
        self.shipping_cost_edit.setText("0.00")
        self.shipping_cost_edit.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.shipping_cost_edit, 1, 1)

        insurance_cost_label = QLabel("تكلفة التأمين:")
        insurance_cost_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        insurance_cost_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(insurance_cost_label, 1, 2)
        self.insurance_cost_edit = QLineEdit()
        self.insurance_cost_edit.setPlaceholderText("0.00")
        self.insurance_cost_edit.setText("0.00")
        self.insurance_cost_edit.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.insurance_cost_edit, 1, 3)

        customs_fees_label = QLabel("رسوم الجمارك:")
        customs_fees_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        customs_fees_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(customs_fees_label, 1, 4)
        self.customs_fees_edit = QLineEdit()
        self.customs_fees_edit.setPlaceholderText("0.00")
        self.customs_fees_edit.setText("0.00")
        self.customs_fees_edit.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.customs_fees_edit, 1, 5)

        # الصف الثالث: رسوم أخرى + إجمالي التكاليف + إجمالي بالعملة المحلية
        other_fees_label = QLabel("رسوم أخرى:")
        other_fees_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        other_fees_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(other_fees_label, 2, 0)
        self.other_fees_edit = QLineEdit()
        self.other_fees_edit.setPlaceholderText("0.00")
        self.other_fees_edit.setText("0.00")
        self.other_fees_edit.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.other_fees_edit, 2, 1)

        total_costs_label = QLabel("إجمالي التكاليف:")
        total_costs_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        total_costs_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(total_costs_label, 2, 2)
        self.total_costs_edit = QLineEdit()
        self.total_costs_edit.setPlaceholderText("0.00")
        self.total_costs_edit.setText("0.00")
        self.total_costs_edit.setReadOnly(True)
        self.total_costs_edit.setStyleSheet(self.get_input_style(readonly=True))
        financial_layout.addWidget(self.total_costs_edit, 2, 3)

        total_local_label = QLabel("الإجمالي بالعملة المحلية:")
        total_local_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        total_local_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        financial_layout.addWidget(total_local_label, 2, 4)
        self.total_local_currency_edit = QLineEdit()
        self.total_local_currency_edit.setPlaceholderText("0.00")
        self.total_local_currency_edit.setText("0.00")
        self.total_local_currency_edit.setReadOnly(True)
        self.total_local_currency_edit.setStyleSheet(self.get_input_style(readonly=True))
        financial_layout.addWidget(self.total_local_currency_edit, 2, 5)

        layout.addWidget(financial_group)

        # مجموعة الدفع
        payment_group = QGroupBox("معلومات الدفع")
        payment_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #27ae60;
                background-color: white;
            }
        """)
        payment_layout = QGridLayout(payment_group)
        payment_layout.setSpacing(12)
        payment_layout.setHorizontalSpacing(8)
        payment_layout.setVerticalSpacing(12)

        # ضبط عرض الأعمدة لجعل التسميات أقرب للحقول
        payment_layout.setColumnMinimumWidth(0, 120)  # عمود التسميات الأول
        payment_layout.setColumnMinimumWidth(1, 150)  # عمود الحقول الأول
        payment_layout.setColumnMinimumWidth(2, 120)  # عمود التسميات الثاني
        payment_layout.setColumnMinimumWidth(3, 150)  # عمود الحقول الثاني
        payment_layout.setColumnMinimumWidth(4, 120)  # عمود التسميات الثالث
        payment_layout.setColumnMinimumWidth(5, 150)  # عمود الحقول الثالث
        payment_layout.setColumnStretch(0, 0)
        payment_layout.setColumnStretch(1, 1)
        payment_layout.setColumnStretch(2, 0)
        payment_layout.setColumnStretch(3, 1)
        payment_layout.setColumnStretch(4, 0)
        payment_layout.setColumnStretch(5, 1)

        # الصف الأول: حالة الدفع + المبلغ المدفوع + المبلغ المتبقي
        payment_status_label = QLabel("حالة الدفع:")
        payment_status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        payment_status_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(payment_status_label, 0, 0)
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItems(["غير مدفوع", "مدفوع جزئياً", "مدفوع بالكامل", "مسترد"])
        self.payment_status_combo.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_status_combo, 0, 1)

        paid_amount_label = QLabel("المبلغ المدفوع:")
        paid_amount_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        paid_amount_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(paid_amount_label, 0, 2)
        self.paid_amount_edit = QLineEdit()
        self.paid_amount_edit.setPlaceholderText("0.00")
        self.paid_amount_edit.setText("0.00")
        self.paid_amount_edit.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.paid_amount_edit, 0, 3)

        remaining_amount_label = QLabel("المبلغ المتبقي:")
        remaining_amount_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        remaining_amount_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(remaining_amount_label, 0, 4)
        self.remaining_amount_edit = QLineEdit()
        self.remaining_amount_edit.setPlaceholderText("0.00")
        self.remaining_amount_edit.setText("0.00")
        self.remaining_amount_edit.setReadOnly(True)
        self.remaining_amount_edit.setStyleSheet(self.get_input_style(readonly=True))
        payment_layout.addWidget(self.remaining_amount_edit, 0, 5)

        # الصف الثاني: تاريخ الدفع + طريقة الدفع
        payment_date_label = QLabel("تاريخ الدفع:")
        payment_date_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        payment_date_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(payment_date_label, 1, 0)
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        self.payment_date_edit.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للتاريخ
        self.payment_date_edit.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        self.payment_date_edit.setDisplayFormat("dd/MM/yyyy")
        payment_layout.addWidget(self.payment_date_edit, 1, 1)

        payment_method_label = QLabel("طريقة الدفع:")
        payment_method_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        payment_method_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; margin-right: 5px; }")
        payment_layout.addWidget(payment_method_label, 1, 2)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"])
        self.payment_method_combo.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_method_combo, 1, 3)

        layout.addWidget(payment_group)
        layout.addStretch()

    def create_shipping_tab(self):
        """إنشاء تبويب الشحن"""
        shipping_tab = QWidget()
        self.tab_widget.addTab(shipping_tab, "🚢 الشحن")

        layout = QVBoxLayout(shipping_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # مجموعة بيانات الشحن
        shipping_group = QGroupBox("بيانات الشحن")
        shipping_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #3498db;
                background-color: white;
            }
        """)
        shipping_layout = QGridLayout(shipping_group)
        shipping_layout.setSpacing(8)

        # الصف الأول: شركة الشحن + نوع الشحن + طريقة الشحن
        shipping_layout.addWidget(QLabel("شركة الشحن:"), 0, 0)
        self.shipping_company_edit = QLineEdit()
        self.shipping_company_edit.setPlaceholderText("اسم شركة الشحن...")
        self.shipping_company_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_company_edit, 0, 1)

        shipping_layout.addWidget(QLabel("نوع الشحن:"), 0, 2)
        self.shipping_type_combo = QComboBox()
        self.shipping_type_combo.addItems(["بحري", "جوي", "بري", "مختلط"])
        self.shipping_type_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_type_combo, 0, 3)

        shipping_layout.addWidget(QLabel("طريقة الشحن:"), 0, 4)
        self.shipping_method_combo = QComboBox()
        self.shipping_method_combo.addItems(["FCL", "LCL", "Air Freight", "Express"])
        self.shipping_method_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_method_combo, 0, 5)

        # الصف الثاني: شروط التسليم + ميناء الشحن + ميناء الوصول
        shipping_layout.addWidget(QLabel("شروط التسليم:"), 1, 0)
        self.incoterms_combo = QComboBox()
        self.incoterms_combo.addItems([
            "EXW", "FCA", "CPT", "CIP", "DAP", "DPU", "DDP",
            "FAS", "FOB", "CFR", "CIF"
        ])
        self.incoterms_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.incoterms_combo, 1, 1)

        shipping_layout.addWidget(QLabel("ميناء الشحن:"), 1, 2)
        self.port_of_loading_edit = QLineEdit()
        self.port_of_loading_edit.setPlaceholderText("ميناء الشحن...")
        self.port_of_loading_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.port_of_loading_edit, 1, 3)

        shipping_layout.addWidget(QLabel("ميناء الوصول:"), 1, 4)
        self.port_of_discharge_edit = QLineEdit()
        self.port_of_discharge_edit.setPlaceholderText("ميناء الوصول...")
        self.port_of_discharge_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.port_of_discharge_edit, 1, 5)

        # الصف الثالث: الوجهة النهائية + اسم السفينة + رقم الرحلة
        shipping_layout.addWidget(QLabel("الوجهة النهائية:"), 2, 0)
        self.final_destination_edit = QLineEdit()
        self.final_destination_edit.setPlaceholderText("الوجهة النهائية...")
        self.final_destination_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.final_destination_edit, 2, 1)

        shipping_layout.addWidget(QLabel("اسم السفينة:"), 2, 2)
        self.vessel_name_edit = QLineEdit()
        self.vessel_name_edit.setPlaceholderText("اسم السفينة...")
        self.vessel_name_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.vessel_name_edit, 2, 3)

        shipping_layout.addWidget(QLabel("رقم الرحلة:"), 2, 4)
        self.voyage_number_edit = QLineEdit()
        self.voyage_number_edit.setPlaceholderText("رقم الرحلة...")
        self.voyage_number_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.voyage_number_edit, 2, 5)

        # الصف الرابع: رقم DHL
        shipping_layout.addWidget(QLabel("رقم DHL:"), 3, 0)
        self.dhl_number_edit = QLineEdit()
        self.dhl_number_edit.setPlaceholderText("رقم DHL...")
        self.dhl_number_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.dhl_number_edit, 3, 1)

        # ملاحظة: تم حذف حقلي "رقم بوليصة الشحن" و "رقم الحاوية الرئيسي"
        # لأنهما موجودان في تبويبات أخرى (تبويب البيانات الأساسية وتبويب الحاويات)

        layout.addWidget(shipping_group)

        # مجموعة التواريخ
        dates_group = QGroupBox("التواريخ")
        dates_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #27ae60;
                background-color: white;
            }
        """)
        dates_layout = QGridLayout(dates_group)
        dates_layout.setSpacing(8)

        # الصف الأول: تاريخ المغادرة المتوقع + الفعلي
        dates_layout.addWidget(QLabel("تاريخ المغادرة المتوقع:"), 0, 0)
        self.estimated_departure_date_edit = QDateEdit()
        self.estimated_departure_date_edit.setDate(QDate.currentDate())
        self.estimated_departure_date_edit.setCalendarPopup(True)
        self.estimated_departure_date_edit.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للتاريخ
        self.estimated_departure_date_edit.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        self.estimated_departure_date_edit.setDisplayFormat("dd/MM/yyyy")
        dates_layout.addWidget(self.estimated_departure_date_edit, 0, 1)

        dates_layout.addWidget(QLabel("تاريخ المغادرة الفعلي:"), 0, 2)
        self.actual_departure_date_edit = QDateEdit()
        self.actual_departure_date_edit.setDate(QDate.currentDate())
        self.actual_departure_date_edit.setCalendarPopup(True)
        self.actual_departure_date_edit.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للتاريخ
        self.actual_departure_date_edit.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        self.actual_departure_date_edit.setDisplayFormat("dd/MM/yyyy")
        dates_layout.addWidget(self.actual_departure_date_edit, 0, 3)

        # الصف الثاني: تاريخ الوصول المتوقع + الفعلي
        dates_layout.addWidget(QLabel("تاريخ الوصول المتوقع:"), 1, 0)
        self.estimated_arrival_date_edit = QDateEdit()
        self.estimated_arrival_date_edit.setDate(QDate.currentDate().addDays(30))
        self.estimated_arrival_date_edit.setCalendarPopup(True)
        self.estimated_arrival_date_edit.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للتاريخ
        self.estimated_arrival_date_edit.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        self.estimated_arrival_date_edit.setDisplayFormat("dd/MM/yyyy")
        dates_layout.addWidget(self.estimated_arrival_date_edit, 1, 1)

        dates_layout.addWidget(QLabel("تاريخ الوصول الفعلي:"), 1, 2)
        self.actual_arrival_date_edit = QDateEdit()
        self.actual_arrival_date_edit.setDate(QDate.currentDate())
        self.actual_arrival_date_edit.setCalendarPopup(True)
        self.actual_arrival_date_edit.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للتاريخ
        self.actual_arrival_date_edit.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        self.actual_arrival_date_edit.setDisplayFormat("dd/MM/yyyy")
        dates_layout.addWidget(self.actual_arrival_date_edit, 1, 3)

        layout.addWidget(dates_group)
        layout.addStretch()

    def create_containers_tab(self):
        """إنشاء تبويب الحاويات"""
        containers_tab = QWidget()
        self.tab_widget.addTab(containers_tab, "📦 الحاويات")

        layout = QVBoxLayout(containers_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # أزرار إدارة الحاويات
        buttons_layout = QHBoxLayout()

        self.add_container_button = QPushButton("إضافة حاوية")
        self.add_container_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.remove_container_button = QPushButton("حذف حاوية")
        self.remove_container_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.remove_container_button.setEnabled(False)

        self.edit_container_button = QPushButton("تعديل حاوية")
        self.edit_container_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        self.edit_container_button.setEnabled(False)

        buttons_layout.addWidget(self.add_container_button)
        buttons_layout.addWidget(self.remove_container_button)
        buttons_layout.addWidget(self.edit_container_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الحاويات
        self.containers_table = QTableWidget()
        self.containers_table.setColumnCount(8)
        self.containers_table.setHorizontalHeaderLabels([
            "رقم الحاوية", "نوع الحاوية", "الحجم", "الوزن الفارغ",
            "الوزن المحمل", "الحالة", "تاريخ التحميل", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.containers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحاوية
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # نوع الحاوية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الحجم
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الوزن الفارغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الوزن المحمل
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ التحميل
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # ملاحظات

        self.containers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.containers_table.setAlternatingRowColors(True)
        self.containers_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.containers_table)

        # معلومات إضافية عن الحاويات
        info_group = QGroupBox("معلومات إضافية")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #3498db;
                background-color: white;
            }
        """)
        info_layout = QFormLayout(info_group)
        info_layout.setSpacing(12)

        # إجمالي الوزن
        self.total_weight_spin = QDoubleSpinBox()
        self.total_weight_spin.setRange(0.00, 999999.99)
        self.total_weight_spin.setDecimals(2)
        self.total_weight_spin.setSuffix(" كجم")
        self.total_weight_spin.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للأرقام
        self.total_weight_spin.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        info_layout.addRow("إجمالي الوزن:", self.total_weight_spin)

        # إجمالي الحجم
        self.total_volume_spin = QDoubleSpinBox()
        self.total_volume_spin.setRange(0.00, 999999.99)
        self.total_volume_spin.setDecimals(2)
        self.total_volume_spin.setSuffix(" م³")
        self.total_volume_spin.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للأرقام
        self.total_volume_spin.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        info_layout.addRow("إجمالي الحجم:", self.total_volume_spin)

        # عدد الطرود
        self.packages_count_spin = QSpinBox()
        self.packages_count_spin.setRange(0, 999999)
        self.packages_count_spin.setStyleSheet(self.get_input_style())
        # تطبيق التنسيق العربي للأرقام
        self.packages_count_spin.setLocale(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
        info_layout.addRow("عدد الطرود:", self.packages_count_spin)

        # نوع التعبئة
        self.packaging_type_combo = QComboBox()
        self.packaging_type_combo.addItems([
            "صناديق", "أكياس", "براميل", "لفائف", "منصات", "أخرى"
        ])
        self.packaging_type_combo.setStyleSheet(self.get_input_style())
        info_layout.addRow("نوع التعبئة:", self.packaging_type_combo)

        # ملاحظات الشحن
        self.shipping_notes_edit = QTextEdit()
        self.shipping_notes_edit.setMaximumHeight(80)
        self.shipping_notes_edit.setPlaceholderText("ملاحظات خاصة بالشحن...")
        self.shipping_notes_edit.setStyleSheet(self.get_input_style())
        info_layout.addRow("ملاحظات الشحن:", self.shipping_notes_edit)

        layout.addWidget(info_group)

        # إجمالي الحاويات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_containers_label = QLabel("إجمالي الحاويات: 0")
        self.total_containers_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_containers_label)

        layout.addLayout(totals_layout)

    def create_documents_tab(self):
        """إنشاء تبويب المستندات"""
        documents_tab = QWidget()
        self.tab_widget.addTab(documents_tab, "📄 المستندات")

        layout = QVBoxLayout(documents_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # قسم روابط المستندات المحددة
        documents_links_group = QGroupBox("روابط المستندات")
        documents_links_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #9b59b6;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #9b59b6;
                background-color: white;
            }
        """)
        documents_links_layout = QGridLayout(documents_links_group)
        documents_links_layout.setSpacing(8)

        # المستندات الأولية
        self.initial_docs_label = QLabel("المستندات الأولية:")
        self.initial_docs_edit = QLineEdit()
        self.initial_docs_edit.setPlaceholderText("رابط المستندات الأولية")
        self.initial_docs_edit.setReadOnly(True)
        self.initial_docs_edit.setStyleSheet(self.get_input_style())
        self.initial_docs_button = QPushButton("رابط")
        self.initial_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 4px 8px; border-radius: 3px; font-size: 11px; }")
        self.initial_docs_attach_button = QPushButton("مرفق")
        self.initial_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 4px 8px; border-radius: 3px; font-size: 11px; }")

        documents_links_layout.addWidget(self.initial_docs_label, 0, 0)
        documents_links_layout.addWidget(self.initial_docs_edit, 0, 1)
        documents_links_layout.addWidget(self.initial_docs_button, 0, 2)
        documents_links_layout.addWidget(self.initial_docs_attach_button, 0, 3)

        # مستندات DN
        self.dn_docs_label = QLabel("المستندات (DN):")
        self.dn_docs_edit = QLineEdit()
        self.dn_docs_edit.setPlaceholderText("رابط مستندات DN")
        self.dn_docs_edit.setReadOnly(True)
        self.dn_docs_edit.setStyleSheet(self.get_input_style())
        self.dn_docs_button = QPushButton("إضافة رابط")
        self.dn_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.dn_docs_attach_button = QPushButton("إضافة مرفق")
        self.dn_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.dn_docs_label, 1, 0)
        documents_links_layout.addWidget(self.dn_docs_edit, 1, 1)
        documents_links_layout.addWidget(self.dn_docs_button, 1, 2)
        documents_links_layout.addWidget(self.dn_docs_attach_button, 1, 3)

        # المستندات المرسلة للجمارك
        self.customs_docs_label = QLabel("المستندات المرسلة للجمارك:")
        self.customs_docs_edit = QLineEdit()
        self.customs_docs_edit.setPlaceholderText("رابط المستندات المرسلة للجمارك")
        self.customs_docs_edit.setReadOnly(True)
        self.customs_docs_edit.setStyleSheet(self.get_input_style())
        self.customs_docs_button = QPushButton("إضافة رابط")
        self.customs_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.customs_docs_attach_button = QPushButton("إضافة مرفق")
        self.customs_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.customs_docs_label, 2, 0)
        documents_links_layout.addWidget(self.customs_docs_edit, 2, 1)
        documents_links_layout.addWidget(self.customs_docs_button, 2, 2)
        documents_links_layout.addWidget(self.customs_docs_attach_button, 2, 3)

        # بوليصة الشحن
        self.bill_lading_label = QLabel("بوليصة الشحن:")
        self.bill_lading_edit = QLineEdit()
        self.bill_lading_edit.setPlaceholderText("رابط بوليصة الشحن")
        self.bill_lading_edit.setReadOnly(True)
        self.bill_lading_edit.setStyleSheet(self.get_input_style())
        self.bill_lading_button = QPushButton("إضافة رابط")
        self.bill_lading_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.bill_lading_attach_button = QPushButton("إضافة مرفق")
        self.bill_lading_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.bill_lading_label, 3, 0)
        documents_links_layout.addWidget(self.bill_lading_edit, 3, 1)
        documents_links_layout.addWidget(self.bill_lading_button, 3, 2)
        documents_links_layout.addWidget(self.bill_lading_attach_button, 3, 3)

        # صور الأصناف
        self.items_images_label = QLabel("صور الأصناف:")
        self.items_images_edit = QLineEdit()
        self.items_images_edit.setPlaceholderText("رابط صور الأصناف")
        self.items_images_edit.setReadOnly(True)
        self.items_images_edit.setStyleSheet(self.get_input_style())
        self.items_images_button = QPushButton("إضافة رابط")
        self.items_images_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.items_images_attach_button = QPushButton("إضافة مرفق")
        self.items_images_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.items_images_label, 4, 0)
        documents_links_layout.addWidget(self.items_images_edit, 4, 1)
        documents_links_layout.addWidget(self.items_images_button, 4, 2)
        documents_links_layout.addWidget(self.items_images_attach_button, 4, 3)

        # مستندات أخرى
        self.other_docs_label = QLabel("مستندات أخرى:")
        self.other_docs_edit = QLineEdit()
        self.other_docs_edit.setPlaceholderText("رابط مستندات أخرى")
        self.other_docs_edit.setReadOnly(True)
        self.other_docs_edit.setStyleSheet(self.get_input_style())
        self.other_docs_button = QPushButton("إضافة رابط")
        self.other_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.other_docs_attach_button = QPushButton("إضافة مرفق")
        self.other_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.other_docs_label, 5, 0)
        documents_links_layout.addWidget(self.other_docs_edit, 5, 1)
        documents_links_layout.addWidget(self.other_docs_button, 5, 2)
        documents_links_layout.addWidget(self.other_docs_attach_button, 5, 3)

        layout.addWidget(documents_links_group)

        # قسم المستندات الإضافية (الجدول القديم)
        additional_docs_group = QGroupBox("مستندات إضافية")
        additional_docs_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #e74c3c;
                background-color: white;
            }
        """)
        additional_docs_layout = QVBoxLayout(additional_docs_group)

        # أزرار إدارة المستندات الإضافية
        buttons_layout = QHBoxLayout()

        self.add_document_button = QPushButton("إضافة مستند")
        self.add_document_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        self.add_link_button = QPushButton("إضافة رابط")
        self.add_link_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        self.remove_document_button = QPushButton("حذف")
        self.remove_document_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.remove_document_button.setEnabled(False)

        self.open_document_button = QPushButton("فتح")
        self.open_document_button.setStyleSheet("QPushButton { background-color: #ff9800; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.open_document_button.setEnabled(False)

        self.add_attachment_button = QPushButton("إضافة مرفق")
        self.add_attachment_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        buttons_layout.addWidget(self.add_document_button)
        buttons_layout.addWidget(self.add_link_button)
        buttons_layout.addWidget(self.add_attachment_button)
        buttons_layout.addWidget(self.remove_document_button)
        buttons_layout.addWidget(self.open_document_button)
        buttons_layout.addStretch()

        additional_docs_layout.addLayout(buttons_layout)

        # جدول المستندات
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels([
            "اسم المستند", "النوع", "المسار/الرابط", "تاريخ الإضافة", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.documents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # اسم المستند
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المسار/الرابط
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # تاريخ الإضافة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # ملاحظات

        self.documents_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.documents_table.setAlternatingRowColors(True)
        self.documents_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        additional_docs_layout.addWidget(self.documents_table)

        # إجمالي المستندات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_documents_label = QLabel("إجمالي المستندات: 0")
        self.total_documents_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_documents_label)

        additional_docs_layout.addLayout(totals_layout)

        layout.addWidget(additional_docs_group)

    def create_dialog_buttons(self, main_layout):
        """إنشاء أزرار الحوار"""
        button_box = QDialogButtonBox()
        button_box.setStyleSheet("""
            QDialogButtonBox {
                background-color: #f8f9fa;
                border-top: 2px solid #dee2e6;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 8px 20px;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        # زر حفظ وإغلاق
        save_close_btn = button_box.addButton("حفظ وإغلاق", QDialogButtonBox.AcceptRole)
        save_close_btn.clicked.connect(self.save_and_close)
        
        # زر إلغاء
        cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        cancel_btn.clicked.connect(self.reject)
        
        main_layout.addWidget(button_box)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.new_button.clicked.connect(self.new_shipment)
        self.save_button.clicked.connect(self.save_shipment)
        self.edit_button.clicked.connect(self.edit_shipment)
        self.import_excel_button.clicked.connect(self.import_from_excel)
        self.exit_button.clicked.connect(self.reject)

        # اتصال زر البحث عن المورد F9
        self.supplier_search_button.clicked.connect(self.search_supplier)

        # اتصالات أزرار الأصناف
        self.add_item_button.clicked.connect(self.add_item)
        self.remove_item_button.clicked.connect(self.remove_item)
        self.edit_item_button.clicked.connect(self.edit_item)

        # اتصالات جدول الأصناف
        self.items_table.itemSelectionChanged.connect(self.on_item_selection_changed)

        # اتصالات أزرار الحاويات
        self.add_container_button.clicked.connect(self.add_container)
        self.remove_container_button.clicked.connect(self.remove_container)
        self.edit_container_button.clicked.connect(self.edit_container)

        # اتصالات جدول الحاويات
        self.containers_table.itemSelectionChanged.connect(self.on_container_selection_changed)

        # اتصالات الحقول المالية لإعادة حساب الإجماليات
        if hasattr(self, 'shipping_cost_spin'):
            self.shipping_cost_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'insurance_cost_spin'):
            self.insurance_cost_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'customs_fees_spin'):
            self.customs_fees_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'other_fees_spin'):
            self.other_fees_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'exchange_rate_spin'):
            self.exchange_rate_spin.valueChanged.connect(self.calculate_totals)
        if hasattr(self, 'paid_amount_spin'):
            self.paid_amount_spin.valueChanged.connect(self.calculate_remaining_amount)

        # اتصالات أزرار المستندات المحددة (الروابط)
        if hasattr(self, 'initial_docs_button'):
            self.initial_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات الأولية", self.initial_docs_edit))
        if hasattr(self, 'dn_docs_button'):
            self.dn_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات (DN)", self.dn_docs_edit))
        if hasattr(self, 'customs_docs_button'):
            self.customs_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات المرسلة للجمارك", self.customs_docs_edit))
        if hasattr(self, 'bill_lading_button'):
            self.bill_lading_button.clicked.connect(lambda: self.add_specific_link("بوليصة الشحن", self.bill_lading_edit))
        if hasattr(self, 'items_images_button'):
            self.items_images_button.clicked.connect(lambda: self.add_specific_link("صور الأصناف", self.items_images_edit))
        if hasattr(self, 'other_docs_button'):
            self.other_docs_button.clicked.connect(lambda: self.add_specific_link("مستندات أخرى", self.other_docs_edit))

        # اتصالات أزرار المستندات المحددة (المرفقات)
        if hasattr(self, 'initial_docs_attach_button'):
            self.initial_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("المستندات الأولية"))
        if hasattr(self, 'dn_docs_attach_button'):
            self.dn_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("المستندات (DN)"))
        if hasattr(self, 'customs_docs_attach_button'):
            self.customs_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("المستندات المرسلة للجمارك"))
        if hasattr(self, 'bill_lading_attach_button'):
            self.bill_lading_attach_button.clicked.connect(lambda: self.add_specific_attachment("بوليصة الشحن"))
        if hasattr(self, 'items_images_attach_button'):
            self.items_images_attach_button.clicked.connect(lambda: self.add_specific_attachment("صور الأصناف"))
        if hasattr(self, 'other_docs_attach_button'):
            self.other_docs_attach_button.clicked.connect(lambda: self.add_specific_attachment("مستندات أخرى"))

        # اتصالات أزرار المستندات الإضافية
        if hasattr(self, 'add_link_button'):
            self.add_link_button.clicked.connect(self.add_link)
        if hasattr(self, 'add_attachment_button'):
            self.add_attachment_button.clicked.connect(self.add_attachment)
        if hasattr(self, 'add_document_button'):
            self.add_document_button.clicked.connect(self.add_document)
        if hasattr(self, 'remove_document_button'):
            self.remove_document_button.clicked.connect(self.remove_document)
        if hasattr(self, 'open_document_button'):
            self.open_document_button.clicked.connect(self.open_document)

        # إعداد اختصارات لوحة المفاتيح
        self.setup_shortcuts()

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # اختصار F9 للبحث عن المورد (في تبويب البيانات الأساسية)
        f9_supplier_shortcut = QShortcut(QKeySequence("F9"), self)
        f9_supplier_shortcut.activated.connect(self.handle_f9_shortcut)

        # اختصار Ctrl+N لإضافة صنف جديد
        add_item_shortcut = QShortcut(QKeySequence("Ctrl+N"), self)
        add_item_shortcut.activated.connect(self.add_item)

        # اختصار Delete لحذف الصنف المحدد
        delete_item_shortcut = QShortcut(QKeySequence("Delete"), self)
        delete_item_shortcut.activated.connect(self.remove_item)

    def handle_f9_shortcut(self):
        """التعامل مع اختصار F9 حسب التبويب النشط"""
        current_tab = self.tab_widget.currentIndex()

        if current_tab == 0:  # تبويب البيانات الأساسية
            self.search_supplier()
        elif current_tab == 1:  # تبويب الأصناف
            self.add_item()
        else:
            # في التبويبات الأخرى، افتراضياً البحث عن المورد
            self.search_supplier()

    def search_supplier(self):
        """البحث عن مورد باستخدام نظام الموردين"""
        try:
            from .supplier_search_dialog import SupplierSearchDialog

            # فتح نافذة البحث عن الموردين
            search_dialog = SupplierSearchDialog(self)

            if search_dialog.exec() == QDialog.Accepted:
                selected_supplier = search_dialog.selected_supplier
                if selected_supplier:
                    # ملء حقل المورد بالاسم والكود
                    supplier_text = f"{selected_supplier.name} ({selected_supplier.code})"
                    self.supplier_edit.setText(supplier_text)

                    # حفظ معرف المورد للاستخدام لاحقاً
                    self.selected_supplier_id = selected_supplier.id
                    # تعيين معرف المورد كخاصية للحقل للتحقق من الصحة
                    self.supplier_edit.setProperty("supplier_id", selected_supplier.id)

                    QMessageBox.information(
                        self,
                        "تم الاختيار",
                        f"✅ تم اختيار المورد: {selected_supplier.name}"
                    )

        except ImportError:
            # في حالة عدم وجود نافذة البحث، استخدم النظام البسيط
            QMessageBox.warning(
                self,
                "تحذير",
                "نافذة البحث عن الموردين غير متوفرة.\nيمكنك كتابة اسم المورد مباشرة في الحقل."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء البحث عن المورد:\n{str(e)}"
            )

    def add_item(self):
        """إضافة صنف جديد من خلال نافذة البحث المتقدمة"""
        try:
            from .advanced_item_search_dialog import AdvancedItemSearchDialog
            from PySide6.QtWidgets import QDialog

            # فتح نافذة البحث المتقدمة
            dialog = AdvancedItemSearchDialog(self)
            if dialog.exec() == QDialog.Accepted:
                selected_item = dialog.get_selected_item()
                if selected_item:
                    # فتح نافذة إدخال السعر والكمية
                    from .item_price_dialog import ItemPriceDialog
                    price_dialog = ItemPriceDialog(self, selected_item)
                    if price_dialog.exec() == QDialog.Accepted:
                        price_data = price_dialog.get_item_data()

                        # إضافة الصنف إلى الجدول
                        row_count = self.items_table.rowCount()
                        self.items_table.insertRow(row_count)

                        # استخدام البيانات المدخلة من النافذة
                        quantity = price_data['quantity']
                        unit_price = price_data['unit_price']
                        total_price = price_data['total_price']
                        notes = price_data['notes']

                        # ملء البيانات
                        self.items_table.setItem(row_count, 0, QTableWidgetItem(selected_item['code']))  # كود الصنف
                        self.items_table.setItem(row_count, 1, QTableWidgetItem(selected_item['name']))  # اسم الصنف
                        self.items_table.setItem(row_count, 2, QTableWidgetItem(str(quantity)))  # الكمية
                        self.items_table.setItem(row_count, 3, QTableWidgetItem(format_number(unit_price, 2, True)))  # سعر الوحدة
                        self.items_table.setItem(row_count, 4, QTableWidgetItem(format_number(total_price, 2, True)))  # السعر الإجمالي
                        self.items_table.setItem(row_count, 5, QTableWidgetItem(format_number(selected_item['weight'], 2, True)))  # الوزن
                        # دمج الملاحظات من النافذة مع وصف الصنف
                        combined_notes = f"{selected_item['description'] or ''}"
                        if notes:
                            combined_notes += f" | {notes}" if combined_notes else notes
                        self.items_table.setItem(row_count, 6, QTableWidgetItem(combined_notes))  # ملاحظات

                        # حفظ معرف الصنف في البيانات المخفية
                        self.items_table.item(row_count, 0).setData(Qt.UserRole, selected_item['id'])

                        # تحديث المجموع الكلي
                        self.update_items_total()

                        QMessageBox.information(
                            self,
                            "تم بنجاح",
                            f"✅ تم إضافة الصنف: {selected_item['name']}\nالكمية: {quantity}\nالسعر: {unit_price:.2f}\nالمجموع: {total_price:.2f}"
                        )

        except ImportError:
            # في حالة عدم وجود نافذة البحث المتقدمة، استخدم الطريقة القديمة
            self.add_item_simple()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}"
            )

    def add_item_simple(self):
        """إضافة صنف بالطريقة البسيطة (احتياطية)"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # الحصول على معلومات الصنف
            item_name, ok = QInputDialog.getText(
                self,
                "إضافة صنف",
                "اسم الصنف:"
            )

            if not ok or not item_name.strip():
                return

            # الحصول على الكمية
            quantity, ok = QInputDialog.getInt(
                self,
                "إضافة صنف",
                "الكمية:",
                1, 1, 999999
            )

            if not ok:
                return

            # الحصول على السعر
            price, ok = QInputDialog.getDouble(
                self,
                "إضافة صنف",
                "السعر:",
                0.0, 0.0, 999999.99, 2
            )

            if not ok:
                return

            # إضافة الصنف إلى الجدول
            row_count = self.items_table.rowCount()
            self.items_table.insertRow(row_count)

            # ملء البيانات
            self.items_table.setItem(row_count, 0, QTableWidgetItem(f"ITEM{row_count + 1:03d}"))  # كود الصنف
            self.items_table.setItem(row_count, 1, QTableWidgetItem(item_name.strip()))  # اسم الصنف
            self.items_table.setItem(row_count, 2, QTableWidgetItem(str(quantity)))  # الكمية
            self.items_table.setItem(row_count, 3, QTableWidgetItem(f"{price:.2f}"))  # سعر الوحدة

            total = quantity * price
            self.items_table.setItem(row_count, 4, QTableWidgetItem(f"{total:.2f}"))  # السعر الإجمالي
            self.items_table.setItem(row_count, 5, QTableWidgetItem("0.00"))  # الوزن (افتراضي)
            self.items_table.setItem(row_count, 6, QTableWidgetItem(""))  # ملاحظات

            # تحديث المجموع الكلي
            self.update_items_total()

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم إضافة الصنف: {item_name}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}"
            )

    def remove_item(self):
        """حذف الصنف المحدد"""
        try:
            current_row = self.items_table.currentRow()

            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد صنف للحذف"
                )
                return

            # الحصول على اسم الصنف
            item_name = self.items_table.item(current_row, 1)
            item_name_text = item_name.text() if item_name else "غير محدد"

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الصنف:\n{item_name_text}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.items_table.removeRow(current_row)

                # إعادة ترقيم الصفوف
                self.renumber_items_table()

                # تحديث المجموع الكلي
                self.update_items_total()

                QMessageBox.information(
                    self,
                    "تم بنجاح",
                    f"✅ تم حذف الصنف: {item_name_text}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء حذف الصنف:\n{str(e)}"
            )

    def edit_item(self):
        """تعديل الصنف المحدد"""
        try:
            current_row = self.items_table.currentRow()

            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد صنف للتعديل"
                )
                return

            # الحصول على البيانات الحالية
            current_name = self.items_table.item(current_row, 1)
            current_quantity = self.items_table.item(current_row, 2)
            current_price = self.items_table.item(current_row, 3)

            current_name_text = current_name.text() if current_name else ""

            # تحويل آمن للكمية
            try:
                current_quantity_value = int(float(current_quantity.text())) if current_quantity and current_quantity.text().strip() else 1
            except (ValueError, AttributeError):
                current_quantity_value = 1

            # تحويل آمن للسعر
            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            current_price_value = safe_float(current_price.text()) if current_price else 0.0

            # تعديل اسم الصنف
            from PySide6.QtWidgets import QInputDialog

            item_name, ok = QInputDialog.getText(
                self,
                "تعديل الصنف",
                "اسم الصنف:",
                text=current_name_text
            )

            if not ok:
                return

            # تعديل الكمية
            quantity, ok = QInputDialog.getInt(
                self,
                "تعديل الصنف",
                "الكمية:",
                current_quantity_value, 1, 999999
            )

            if not ok:
                return

            # تعديل السعر
            price, ok = QInputDialog.getDouble(
                self,
                "تعديل الصنف",
                "السعر:",
                current_price_value, 0.0, 999999.99, 2
            )

            if not ok:
                return

            # تحديث البيانات في الجدول
            self.items_table.setItem(current_row, 1, QTableWidgetItem(item_name.strip()))
            self.items_table.setItem(current_row, 2, QTableWidgetItem(str(quantity)))
            self.items_table.setItem(current_row, 3, QTableWidgetItem(f"{price:.2f}"))

            total = quantity * price
            self.items_table.setItem(current_row, 4, QTableWidgetItem(f"{total:.2f}"))

            # تحديث المجموع الكلي
            self.update_items_total()

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم تعديل الصنف: {item_name}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تعديل الصنف:\n{str(e)}"
            )

    def on_item_selection_changed(self):
        """تفعيل/إلغاء تفعيل أزرار الأصناف حسب التحديد"""
        has_selection = self.items_table.currentRow() >= 0
        self.remove_item_button.setEnabled(has_selection)
        self.edit_item_button.setEnabled(has_selection)

    def renumber_items_table(self):
        """إعادة ترقيم جدول الأصناف"""
        for row in range(self.items_table.rowCount()):
            self.items_table.setItem(row, 0, QTableWidgetItem(f"ITEM{row + 1:03d}"))

    def update_items_total(self):
        """تحديث المجموع الكلي للأصناف"""
        try:
            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية وإزالة الفواصل
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            total = 0.0
            for row in range(self.items_table.rowCount()):
                total_item = self.items_table.item(row, 4)  # عمود المجموع
                if total_item:
                    total += safe_float(total_item.text())

            # تحديث حقل المجموع في تبويب المالية
            if hasattr(self, 'total_amount_edit'):
                self.total_amount_edit.setValue(total)

            # تحديث قيمة البضاعة في التبويب المالي
            if hasattr(self, 'goods_value_edit'):
                self.goods_value_edit.setText(f"{total:.2f}")

            # إعادة حساب الإجماليات المالية
            self.calculate_totals()

        except Exception as e:
            print(f"خطأ في تحديث المجموع: {str(e)}")

    def calculate_totals(self):
        """حساب الإجماليات المالية"""
        try:
            # قيمة البضاعة من جدول الأصناف
            goods_value = 0.0
            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            for row in range(self.items_table.rowCount()):
                try:
                    total_item = self.items_table.item(row, 4)
                    if total_item:
                        goods_value += safe_float(total_item.text())
                except (ValueError, AttributeError):
                    continue

            # تحديث قيمة البضاعة
            if hasattr(self, 'goods_value_edit'):
                self.goods_value_edit.setText(f"{goods_value:.2f}")

            # حساب إجمالي التكاليف (استخدام نفس دالة safe_float المعرفة أعلاه)
            shipping_cost = safe_float(self.shipping_cost_edit.text()) if hasattr(self, 'shipping_cost_edit') else 0.0
            insurance_cost = safe_float(self.insurance_cost_edit.text()) if hasattr(self, 'insurance_cost_edit') else 0.0
            customs_fees = safe_float(self.customs_fees_edit.text()) if hasattr(self, 'customs_fees_edit') else 0.0
            other_fees = safe_float(self.other_fees_edit.text()) if hasattr(self, 'other_fees_edit') else 0.0

            total_costs = goods_value + shipping_cost + insurance_cost + customs_fees + other_fees

            if hasattr(self, 'total_costs_edit'):
                self.total_costs_edit.setText(f"{total_costs:.2f}")

            # حساب المبلغ بالعملة المحلية
            exchange_rate = safe_float(self.exchange_rate_edit.text()) if hasattr(self, 'exchange_rate_edit') else 1.0
            total_local = total_costs * exchange_rate

            if hasattr(self, 'total_local_currency_edit'):
                self.total_local_currency_edit.setText(f"{total_local:.2f}")

            # حساب المبلغ المتبقي
            self.calculate_remaining_amount()

        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {str(e)}")

    def calculate_remaining_amount(self):
        """حساب المبلغ المتبقي"""
        try:
            if not hasattr(self, 'total_local_currency_spin') or not hasattr(self, 'paid_amount_spin'):
                return

            def safe_float(text):
                try:
                    if not text or not text.strip():
                        return 0.0
                    # استبدال الفاصلة العربية بالنقطة الإنجليزية
                    clean_text = text.replace('،', '.').replace(',', '.')
                    return float(clean_text)
                except (ValueError, AttributeError):
                    return 0.0

            total_amount = safe_float(self.total_local_currency_edit.text()) if hasattr(self, 'total_local_currency_edit') else 0.0
            paid_amount = safe_float(self.paid_amount_edit.text()) if hasattr(self, 'paid_amount_edit') else 0.0
            remaining = max(0, total_amount - paid_amount)

            if hasattr(self, 'remaining_amount_edit'):
                self.remaining_amount_edit.setText(f"{remaining:.2f}")

            # تحديث حالة الدفع تلقائياً
            if hasattr(self, 'payment_status_combo'):
                if remaining == 0 and total_amount > 0:
                    self.payment_status_combo.setCurrentText("تم الدفع بالكامل")
                elif paid_amount > 0 and remaining > 0:
                    self.payment_status_combo.setCurrentText("دفع جزئي")
                elif paid_amount == 0:
                    self.payment_status_combo.setCurrentText("لم يتم الدفع")

        except Exception as e:
            print(f"خطأ في حساب المبلغ المتبقي: {str(e)}")

    # ==================== وظائف إدارة الحاويات ====================

    def add_container(self):
        """إضافة حاوية جديدة"""
        try:
            from .container_dialog import ContainerDialog
            from PySide6.QtWidgets import QDialog

            dialog = ContainerDialog(self)
            if dialog.exec() == QDialog.Accepted:
                container_data = dialog.get_container_data()
                if container_data:
                    self.add_container_to_table(container_data)

        except ImportError:
            # في حالة عدم وجود نافذة الحاوية، استخدم طريقة بسيطة
            self.add_container_simple()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الحاوية:\n{str(e)}"
            )

    def add_container_simple(self):
        """إضافة حاوية بالطريقة البسيطة (احتياطية)"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # طلب رقم الحاوية
            container_number, ok = QInputDialog.getText(
                self,
                "إضافة حاوية",
                "رقم الحاوية:",
                text=""
            )

            if not ok or not container_number.strip():
                return

            # التحقق من عدم وجود الحاوية مسبقاً
            for row in range(self.containers_table.rowCount()):
                existing_number = self.containers_table.item(row, 0).text()
                if existing_number == container_number.strip():
                    QMessageBox.warning(self, "تحذير", "هذه الحاوية موجودة بالفعل في الشحنة")
                    return

            # إضافة الحاوية إلى الجدول
            row_count = self.containers_table.rowCount()
            self.containers_table.insertRow(row_count)

            # ملء البيانات الافتراضية
            self.containers_table.setItem(row_count, 0, QTableWidgetItem(container_number.strip()))  # رقم الحاوية
            self.containers_table.setItem(row_count, 1, QTableWidgetItem("20' Standard"))  # نوع الحاوية
            self.containers_table.setItem(row_count, 2, QTableWidgetItem("20 قدم"))  # الحجم
            self.containers_table.setItem(row_count, 3, QTableWidgetItem("2300.00"))  # الوزن الفارغ
            self.containers_table.setItem(row_count, 4, QTableWidgetItem("0.00"))  # الوزن المحمل
            self.containers_table.setItem(row_count, 5, QTableWidgetItem("فارغة"))  # الحالة
            self.containers_table.setItem(row_count, 6, QTableWidgetItem(""))  # تاريخ التحميل
            self.containers_table.setItem(row_count, 7, QTableWidgetItem(""))  # ملاحظات

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم إضافة الحاوية: {container_number}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الحاوية:\n{str(e)}"
            )

    def add_container_to_table(self, container_data):
        """إضافة حاوية إلى الجدول"""
        try:
            # التحقق من عدم وجود الحاوية مسبقاً
            for row in range(self.containers_table.rowCount()):
                existing_number = self.containers_table.item(row, 0).text()
                if existing_number == container_data['container_number']:
                    QMessageBox.warning(self, "تحذير", "هذه الحاوية موجودة بالفعل في الشحنة")
                    return

            # إضافة صف جديد
            row_count = self.containers_table.rowCount()
            self.containers_table.insertRow(row_count)

            # ملء البيانات
            self.containers_table.setItem(row_count, 0, QTableWidgetItem(container_data['container_number']))
            self.containers_table.setItem(row_count, 1, QTableWidgetItem(container_data['container_type']))
            self.containers_table.setItem(row_count, 2, QTableWidgetItem(container_data['size']))
            self.containers_table.setItem(row_count, 3, QTableWidgetItem(f"{container_data['empty_weight']:.2f}"))
            self.containers_table.setItem(row_count, 4, QTableWidgetItem(f"{container_data['loaded_weight']:.2f}"))
            self.containers_table.setItem(row_count, 5, QTableWidgetItem(container_data['status']))
            self.containers_table.setItem(row_count, 6, QTableWidgetItem(""))  # تاريخ التحميل
            self.containers_table.setItem(row_count, 7, QTableWidgetItem(container_data['notes']))

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم إضافة الحاوية: {container_data['container_number']}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء إضافة الحاوية إلى الجدول:\n{str(e)}"
            )

    def remove_container(self):
        """حذف الحاوية المحددة"""
        try:
            current_row = self.containers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد حاوية لحذفها"
                )
                return

            # الحصول على رقم الحاوية
            container_number = self.containers_table.item(current_row, 0).text()

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الحاوية:\n{container_number}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.containers_table.removeRow(current_row)
                QMessageBox.information(
                    self,
                    "تم بنجاح",
                    f"✅ تم حذف الحاوية: {container_number}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء حذف الحاوية:\n{str(e)}"
            )

    def edit_container(self):
        """تعديل الحاوية المحددة"""
        try:
            current_row = self.containers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "يرجى تحديد حاوية لتعديلها"
                )
                return

            try:
                from .container_dialog import ContainerDialog

                def safe_float(text):
                    try:
                        if not text or not text.strip():
                            return 0.0
                        # استبدال الفاصلة العربية بالنقطة الإنجليزية
                        clean_text = text.replace('،', '.').replace(',', '.')
                        return float(clean_text)
                    except (ValueError, AttributeError):
                        return 0.0

                # الحصول على البيانات الحالية
                current_data = {
                    'container_number': self.containers_table.item(current_row, 0).text(),
                    'container_type': self.containers_table.item(current_row, 1).text(),
                    'size': self.containers_table.item(current_row, 2).text(),
                    'empty_weight': safe_float(self.containers_table.item(current_row, 3).text()),
                    'loaded_weight': safe_float(self.containers_table.item(current_row, 4).text()),
                    'status': self.containers_table.item(current_row, 5).text(),
                    'notes': self.containers_table.item(current_row, 7).text()
                }

                dialog = ContainerDialog(self, current_data)
                if dialog.exec() == QDialog.Accepted:
                    container_data = dialog.get_container_data()
                    if container_data:
                        # تحديث البيانات في الجدول
                        self.containers_table.item(current_row, 0).setText(container_data['container_number'])
                        self.containers_table.item(current_row, 1).setText(container_data['container_type'])
                        self.containers_table.item(current_row, 2).setText(container_data['size'])
                        self.containers_table.item(current_row, 3).setText(f"{container_data['empty_weight']:.2f}")
                        self.containers_table.item(current_row, 4).setText(f"{container_data['loaded_weight']:.2f}")
                        self.containers_table.item(current_row, 5).setText(container_data['status'])
                        self.containers_table.item(current_row, 7).setText(container_data['notes'])

                        QMessageBox.information(
                            self,
                            "تم بنجاح",
                            f"✅ تم تعديل الحاوية: {container_data['container_number']}"
                        )

            except ImportError:
                # في حالة عدم وجود نافذة الحاوية، استخدم طريقة بسيطة
                self.edit_container_simple(current_row)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تعديل الحاوية:\n{str(e)}"
            )

    def edit_container_simple(self, current_row):
        """تعديل حاوية بالطريقة البسيطة (احتياطية)"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # الحصول على البيانات الحالية
            current_number = self.containers_table.item(current_row, 0).text()
            current_status = self.containers_table.item(current_row, 5).text()

            # تعديل رقم الحاوية
            container_number, ok = QInputDialog.getText(
                self,
                "تعديل الحاوية",
                "رقم الحاوية:",
                text=current_number
            )

            if not ok:
                return

            # تعديل الحالة
            status_items = ["فارغة", "محملة", "في الطريق", "وصلت", "تم التفريغ"]
            status, ok = QInputDialog.getItem(
                self,
                "تعديل الحاوية",
                "حالة الحاوية:",
                status_items,
                status_items.index(current_status) if current_status in status_items else 0,
                False
            )

            if not ok:
                return

            # تحديث البيانات في الجدول
            self.containers_table.item(current_row, 0).setText(container_number.strip())
            self.containers_table.item(current_row, 5).setText(status)

            QMessageBox.information(
                self,
                "تم بنجاح",
                f"✅ تم تعديل الحاوية: {container_number}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء تعديل الحاوية:\n{str(e)}"
            )

    def on_container_selection_changed(self):
        """تفعيل/إلغاء تفعيل أزرار الحاويات حسب التحديد"""
        has_selection = self.containers_table.currentRow() >= 0
        self.remove_container_button.setEnabled(has_selection)
        self.edit_container_button.setEnabled(has_selection)

    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        reply = QMessageBox.question(
            self,
            "شحنة جديدة",
            "هل تريد إنشاء شحنة جديدة؟\nسيتم مسح البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_form()
            self.current_shipment_id = None
            self.edit_button.setEnabled(False)

            # سؤال المستخدم إذا كان يريد بيانات تجريبية
            test_reply = QMessageBox.question(
                self,
                "بيانات تجريبية",
                "هل تريد ملء النموذج ببيانات تجريبية للاختبار؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if test_reply == QMessageBox.Yes:
                self.fill_test_data()

            QMessageBox.information(self, "شحنة جديدة", "✅ تم إنشاء نموذج شحنة جديد")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.shipment_number_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الشحنة")
            return False

        if not self.supplier_edit.property("supplier_id"):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            return False

        # التحقق من عدم تكرار رقم الشحنة
        if not self.check_shipment_number_unique():
            return False

        return True

    def check_shipment_number_unique(self):
        """التحقق من أن رقم الشحنة فريد"""
        shipment_number = self.shipment_number_edit.text().strip()

        session = self.db_manager.get_session()
        try:
            # في وضع التعديل، استثناء الشحنة الحالية من التحقق
            query = session.query(Shipment).filter(
                Shipment.shipment_number == shipment_number
            )

            if self.is_edit_mode and self.current_shipment_id:
                query = query.filter(Shipment.id != self.current_shipment_id)

            existing_shipment = query.first()

            if existing_shipment:
                # اقتراح رقم جديد
                new_number = self.generate_unique_shipment_number()

                reply = QMessageBox.question(
                    self,
                    "رقم الشحنة مكرر",
                    f"رقم الشحنة '{shipment_number}' موجود مسبقاً.\n\n"
                    f"هل تريد استخدام الرقم المقترح: '{new_number}'؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    self.shipment_number_edit.setText(new_number)
                    return True
                else:
                    return False

            return True

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في التحقق من رقم الشحنة: {str(e)}")
            return False
        finally:
            session.close()

    def generate_unique_shipment_number(self):
        """توليد رقم شحنة فريد"""
        from datetime import datetime
        import random
        import time

        session = self.db_manager.get_session()
        try:
            # الحصول على السنة الحالية
            current_year = datetime.now().year

            # البحث عن جميع أرقام الشحنات في هذه السنة
            existing_numbers = session.query(Shipment.shipment_number).filter(
                Shipment.shipment_number.like(f'SH-{current_year}-%')
            ).all()

            # استخراج الأرقام التسلسلية الموجودة
            used_numbers = set()
            for (number,) in existing_numbers:
                try:
                    # استخراج الرقم التسلسلي من نهاية رقم الشحنة
                    parts = number.split('-')
                    if len(parts) >= 3:
                        sequential_num = int(parts[-1])
                        used_numbers.add(sequential_num)
                except (ValueError, IndexError):
                    continue

            # العثور على أول رقم متاح
            next_number = 1
            while next_number in used_numbers:
                next_number += 1

            # إضافة عنصر عشوائي لضمان الفرادة في كل استدعاء
            timestamp_suffix = int(time.time() * 1000) % 1000
            next_number = next_number * 1000 + timestamp_suffix

            # توليد رقم الشحنة الجديد
            new_number = f"SH-{current_year}-{next_number:06d}"

            # التأكد النهائي من أن الرقم فريد
            attempt = 0
            while session.query(Shipment).filter(Shipment.shipment_number == new_number).first() and attempt < 100:
                next_number += 1
                new_number = f"SH-{current_year}-{next_number:06d}"
                attempt += 1

            if attempt >= 100:
                # في حالة فشل العثور على رقم فريد، استخدم رقم عشوائي
                random_num = random.randint(100000, 999999)
                new_number = f"SH-{current_year}-{random_num}"

            return new_number

        except Exception as e:
            # في حالة الخطأ، استخدم رقم عشوائي مع الوقت
            timestamp = int(time.time()) % 100000
            return f"SH-{datetime.now().year}-{timestamp:05d}"
        finally:
            session.close()

    def add_specific_link(self, link_type, target_edit):
        """إضافة رابط محدد لنوع مستند معين"""
        try:
            from .add_link_dialog import AddLinkDialog

            existing_url = target_edit.text().strip()
            existing_description = ""

            dialog = AddLinkDialog(self, link_type, existing_url, existing_description)
            if dialog.exec() == QDialog.Accepted:
                link_data = dialog.get_link_data()
                url = link_data['url']
                description = link_data['description']

                # تحديث الحقل
                target_edit.setText(url)
                target_edit.setReadOnly(False)  # السماح بالقراءة للنقر

                # تطبيق تنسيق الرابط التشعبي
                target_edit.setStyleSheet("""
                    QLineEdit {
                        color: #0066cc;
                        text-decoration: underline;
                        background-color: #f0f8ff;
                        border: 2px solid #0066cc;
                        border-radius: 3px;
                        padding: 5px;
                    }
                    QLineEdit:hover {
                        background-color: #e6f3ff;
                    }
                """)

                # إضافة إمكانية النقر لفتح الرابط
                target_edit.mousePressEvent = lambda event: self.open_url(url) if event.button() == Qt.LeftButton else None

                # إضافة tooltip مع الوصف
                if description:
                    target_edit.setToolTip(f"{link_type}\n{description}\nانقر لفتح الرابط")
                else:
                    target_edit.setToolTip(f"{link_type}\nانقر لفتح الرابط")

                QMessageBox.information(self, "تم الحفظ", f"تم حفظ رابط {link_type} بنجاح")

        except ImportError:
            QMessageBox.warning(self, "خطأ", "لا يمكن العثور على نافذة إضافة الرابط")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الرابط: {str(e)}")

    def add_specific_attachment(self, document_type):
        """إضافة مرفق محدد لنوع مستند معين"""
        try:
            from .attachments_manager_dialog import AttachmentsManagerDialog

            dialog = AttachmentsManagerDialog(self, document_type)
            if dialog.exec() == QDialog.Accepted:
                attachments = dialog.get_attachments()
                if attachments:
                    QMessageBox.information(
                        self, "تم الحفظ",
                        f"تم حفظ {len(attachments)} مرفق لـ {document_type}"
                    )

        except ImportError:
            QMessageBox.warning(self, "خطأ", "لا يمكن العثور على نافذة إدارة المرفقات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المرفق: {str(e)}")

    def open_url(self, url):
        """فتح رابط في المتصفح"""
        try:
            import webbrowser
            webbrowser.open(url)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"لا يمكن فتح الرابط: {str(e)}")

    def add_link(self):
        """إضافة رابط تشعبي للمستندات الإضافية"""
        try:
            from PySide6.QtWidgets import QInputDialog

            # طلب الرابط
            url, ok = QInputDialog.getText(self, "إضافة رابط", "أدخل الرابط:")
            if not ok or not url.strip():
                return

            # طلب اسم الرابط
            name, ok = QInputDialog.getText(self, "اسم الرابط", "أدخل اسم الرابط:")
            if not ok or not name.strip():
                name = url

            self.add_document_to_table(name, "رابط", url, "رابط")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الرابط: {str(e)}")

    def add_attachment(self):
        """إضافة مرفق للمستندات الإضافية"""
        try:
            from .attachments_manager_dialog import AttachmentsManagerDialog

            dialog = AttachmentsManagerDialog(self, "مستندات إضافية")
            if dialog.exec() == QDialog.Accepted:
                attachments = dialog.get_attachments()
                for attachment in attachments:
                    self.add_document_to_table(
                        attachment['name'],
                        "مرفق",
                        attachment['path'],
                        "مرفق"
                    )

        except ImportError:
            QMessageBox.warning(self, "خطأ", "لا يمكن العثور على نافذة إدارة المرفقات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المرفق: {str(e)}")

    def add_document(self):
        """إضافة مستند عام"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار مستند",
                "",
                "جميع الملفات (*.*)"
            )

            if file_path:
                import os
                file_name = os.path.basename(file_path)
                self.add_document_to_table(file_name, "مستند", file_path, "ملف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المستند: {str(e)}")

    def remove_document(self):
        """حذف مستند من الجدول"""
        try:
            current_row = self.documents_table.currentRow()
            if current_row >= 0:
                reply = QMessageBox.question(
                    self, "تأكيد الحذف",
                    "هل أنت متأكد من حذف هذا المستند؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.documents_table.removeRow(current_row)
            else:
                QMessageBox.warning(self, "تحذير", "يجب اختيار مستند أولاً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف المستند: {str(e)}")

    def open_document(self):
        """فتح المستند المحدد"""
        try:
            current_row = self.documents_table.currentRow()
            if current_row >= 0:
                path_item = self.documents_table.item(current_row, 2)
                if path_item:
                    path = path_item.text()
                    if path.startswith('http'):
                        self.open_url(path)
                    else:
                        import os
                        os.startfile(path)
            else:
                QMessageBox.warning(self, "تحذير", "يجب اختيار مستند أولاً")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح المستند: {str(e)}")

    def add_document_to_table(self, name, doc_type, path, category):
        """إضافة مستند إلى الجدول"""
        try:
            row = self.documents_table.rowCount()
            self.documents_table.insertRow(row)

            self.documents_table.setItem(row, 0, QTableWidgetItem(name))
            self.documents_table.setItem(row, 1, QTableWidgetItem(doc_type))
            self.documents_table.setItem(row, 2, QTableWidgetItem(path))
            self.documents_table.setItem(row, 3, QTableWidgetItem(category))
            self.documents_table.setItem(row, 4, QTableWidgetItem("نشط"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المستند للجدول: {str(e)}")

    def generate_new_shipment_number(self):
        """توليد رقم شحنة جديد وتحديث الحقل"""
        try:
            new_number = self.generate_unique_shipment_number()
            self.shipment_number_edit.setText(new_number)
            QMessageBox.information(
                self,
                "رقم جديد",
                f"✅ تم توليد رقم شحنة جديد: {new_number}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"خطأ في توليد رقم الشحنة: {str(e)}"
            )

    def get_shipment_items(self):
        """الحصول على أصناف الشحنة من الجدول"""
        def safe_float(text):
            try:
                if not text or not text.strip():
                    return 0.0
                # استبدال الفاصلة العربية بالنقطة الإنجليزية
                clean_text = text.replace('،', '.').replace(',', '.')
                return float(clean_text)
            except (ValueError, AttributeError):
                return 0.0

        items = []
        try:
            for row in range(self.items_table.rowCount()):
                item_id = self.items_table.item(row, 0).data(Qt.UserRole)
                if item_id:
                    quantity = safe_float(self.items_table.item(row, 2).text())
                    unit_price = safe_float(self.items_table.item(row, 3).text())
                    total_price = safe_float(self.items_table.item(row, 4).text())

                    items.append({
                        'item_id': item_id,
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'total_price': total_price
                    })
            return items
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في قراءة أصناف الشحنة: {str(e)}")
            return None

    def save_shipment(self):
        """حفظ الشحنة"""
        if not self.validate_data():
            return False

        # الحصول على أصناف الشحنة
        shipment_items = self.get_shipment_items()
        if shipment_items is None:
            return False

        session = self.db_manager.get_session()
        try:
            # حساب إجمالي المبلغ
            total_amount = sum(item['total_price'] for item in shipment_items)

            # تعريف متغير الشحنة
            new_shipment = None
            shipment_id = None

            if self.is_edit_mode and self.current_shipment_id:
                # وضع التعديل - تحديث الشحنة الموجودة
                new_shipment = session.query(Shipment).filter(
                    Shipment.id == self.current_shipment_id
                ).first()

                if not new_shipment:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على الشحنة للتعديل")
                    return False

                shipment_id = new_shipment.id

                # تحديث بيانات الشحنة
                new_shipment.shipment_number = self.shipment_number_edit.text()
                new_shipment.supplier_id = self.supplier_edit.property("supplier_id")
                new_shipment.supplier_invoice_number = self.supplier_invoice_edit.text()
                new_shipment.shipment_status = self.shipment_status_combo.currentText()
                new_shipment.clearance_status = self.clearance_status_combo.currentText()
                new_shipment.tracking_number = self.tracking_number_edit.text()
                new_shipment.bill_of_lading = self.bill_of_lading_edit.text()
                new_shipment.notes = self.notes_edit.toPlainText()
                new_shipment.total_amount = total_amount

                # تحديث الحقول الإضافية
                if hasattr(self, 'shipping_policy_edit'):
                    new_shipment.shipping_policy = self.shipping_policy_edit.text()
                if hasattr(self, 'container_number_edit'):
                    new_shipment.container_number = self.container_number_edit.text()
                if hasattr(self, 'shipping_company_edit'):
                    new_shipment.shipping_company = self.shipping_company_edit.text()

                # حذف الأصناف والحاويات القديمة
                session.query(ShipmentItem).filter(
                    ShipmentItem.shipment_id == self.current_shipment_id
                ).delete()
                session.query(Container).filter(
                    Container.shipment_id == self.current_shipment_id
                ).delete()

                shipment_id = self.current_shipment_id

            else:
                # وضع الإنشاء - إنشاء شحنة جديدة
                new_shipment = Shipment(
                    shipment_number=self.shipment_number_edit.text(),
                    supplier_id=self.supplier_edit.property("supplier_id"),
                    supplier_invoice_number=self.supplier_invoice_edit.text(),
                    shipment_status=self.shipment_status_combo.currentText(),
                    clearance_status=self.clearance_status_combo.currentText(),
                    tracking_number=self.tracking_number_edit.text(),
                    bill_of_lading=self.bill_of_lading_edit.text(),
                    notes=self.notes_edit.toPlainText(),
                    total_amount=total_amount
                )

                # إضافة الحقول الإضافية
                if hasattr(self, 'shipping_policy_edit'):
                    new_shipment.shipping_policy = self.shipping_policy_edit.text()
                if hasattr(self, 'container_number_edit'):
                    new_shipment.container_number = self.container_number_edit.text()
                if hasattr(self, 'shipping_company_edit'):
                    new_shipment.shipping_company = self.shipping_company_edit.text()

                session.add(new_shipment)
                session.flush()  # للحصول على ID الشحنة
                shipment_id = new_shipment.id
                self.current_shipment_id = shipment_id

            # حفظ أصناف الشحنة
            for item_data in shipment_items:
                shipment_item = ShipmentItem(
                    shipment_id=shipment_id,
                    item_id=item_data['item_id'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    total_price=item_data['total_price']
                )
                session.add(shipment_item)

            # حفظ الحاويات
            self.save_containers(session, shipment_id)

            session.commit()

            # تحديث معرف الشحنة الحالية
            if new_shipment:
                self.current_shipment_id = new_shipment.id
                self.edit_button.setEnabled(True)
                self.shipment_saved.emit(new_shipment.id)
                QMessageBox.information(self, "نجح", f"✅ تم حفظ الشحنة رقم {new_shipment.shipment_number} بنجاح")
            else:
                # في حالة التعديل، استخدم الشحنة المحدثة
                self.edit_button.setEnabled(True)
                if hasattr(self, 'shipment_saved'):
                    self.shipment_saved.emit(self.current_shipment_id)
                QMessageBox.information(self, "نجح", f"✅ تم تحديث الشحنة بنجاح")

            return True

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الشحنة:\n{str(e)}")
            return False
        finally:
            session.close()

    def save_containers(self, session, shipment_id):
        """حفظ الحاويات"""
        def safe_float(text):
            try:
                if not text or not text.strip():
                    return 0.0
                # استبدال الفاصلة العربية بالنقطة الإنجليزية
                clean_text = text.replace('،', '.').replace(',', '.')
                return float(clean_text)
            except (ValueError, AttributeError):
                return 0.0

        try:
            for row in range(self.containers_table.rowCount()):
                container_number = self.containers_table.item(row, 0).text()
                container_type = self.containers_table.item(row, 1).text()
                container_size = self.containers_table.item(row, 2).text()
                empty_weight = safe_float(self.containers_table.item(row, 3).text())
                loaded_weight = safe_float(self.containers_table.item(row, 4).text())
                status = self.containers_table.item(row, 5).text()
                notes = self.containers_table.item(row, 7).text() if self.containers_table.item(row, 7) else ""

                container = Container(
                    shipment_id=shipment_id,
                    container_number=container_number,
                    container_type=container_type,
                    container_size=container_size,
                    weight_empty=empty_weight,
                    weight_loaded=loaded_weight,
                    status=status,
                    notes=notes
                )
                session.add(container)
        except Exception as e:
            print(f"خطأ في حفظ الحاويات: {str(e)}")
    
    def edit_shipment(self):
        """تعديل الشحنة"""
        if not self.is_edit_mode:
            QMessageBox.warning(self, "تحذير", "لا توجد شحنة محملة للتعديل")
            return

        QMessageBox.information(self, "تعديل", "✏️ يمكنك الآن تعديل بيانات الشحنة وحفظها")

    def load_shipment_data(self):
        """تحميل بيانات الشحنة للتعديل"""
        if not self.current_shipment_id:
            return

        session = self.db_manager.get_session()
        try:
            # تحميل بيانات الشحنة
            from ...database.models import Supplier
            shipment = session.query(Shipment).filter(
                Shipment.id == self.current_shipment_id
            ).first()

            if not shipment:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الشحنة المطلوبة")
                return

            # ملء البيانات الأساسية
            self.shipment_number_edit.setText(shipment.shipment_number or "")

            # تحميل تاريخ الشحنة - استخدام تاريخ المغادرة المتوقع أو التاريخ الحالي
            if hasattr(self, 'shipment_date_edit'):
                try:
                    # البحث عن أي تاريخ متاح في الشحنة
                    date_to_use = None
                    if shipment.estimated_departure_date:
                        date_to_use = shipment.estimated_departure_date
                    elif shipment.actual_departure_date:
                        date_to_use = shipment.actual_departure_date
                    elif shipment.estimated_arrival_date:
                        date_to_use = shipment.estimated_arrival_date
                    elif shipment.created_at:
                        date_to_use = shipment.created_at

                    if date_to_use and hasattr(date_to_use, 'year'):
                        # إذا كان التاريخ من نوع datetime
                        self.shipment_date_edit.setDate(QDate(
                            date_to_use.year,
                            date_to_use.month,
                            date_to_use.day
                        ))
                    else:
                        self.shipment_date_edit.setDate(QDate.currentDate())
                except Exception as e:
                    print(f"خطأ في تحميل التاريخ: {e}")
                    self.shipment_date_edit.setDate(QDate.currentDate())

            # ملء بيانات المورد
            if shipment.supplier:
                self.supplier_edit.setText(shipment.supplier.name)
                self.selected_supplier_id = shipment.supplier_id
                self.supplier_edit.setProperty("supplier_id", shipment.supplier_id)

            # ملء الحقول الأخرى
            if hasattr(self, 'shipment_status_combo'):
                status_index = self.shipment_status_combo.findText(shipment.shipment_status or "تحت الطلب")
                if status_index >= 0:
                    self.shipment_status_combo.setCurrentIndex(status_index)

            if hasattr(self, 'clearance_status_combo'):
                clearance_index = self.clearance_status_combo.findText(shipment.clearance_status or "بدون الافراج")
                if clearance_index >= 0:
                    self.clearance_status_combo.setCurrentIndex(clearance_index)

            if hasattr(self, 'notes_edit'):
                self.notes_edit.setPlainText(shipment.notes or "")

            if hasattr(self, 'tracking_number_edit'):
                self.tracking_number_edit.setText(shipment.tracking_number or "")

            if hasattr(self, 'bill_of_lading_edit'):
                self.bill_of_lading_edit.setText(shipment.bill_of_lading or "")

            if hasattr(self, 'supplier_invoice_edit'):
                self.supplier_invoice_edit.setText(shipment.supplier_invoice_number or "")

            # ملء الحقول الإضافية (التي تم إضافتها مؤخراً)
            if hasattr(self, 'shipping_policy_edit'):
                self.shipping_policy_edit.setText(shipment.shipping_policy or "")
            if hasattr(self, 'container_number_edit'):
                self.container_number_edit.setText(shipment.container_number or "")
            if hasattr(self, 'shipping_company_edit'):
                self.shipping_company_edit.setText(shipment.shipping_company or "")

            # تحميل الأصناف
            self.load_shipment_items(shipment.id)

            # تحميل الحاويات
            self.load_shipment_containers(shipment.id)

            print(f"✅ تم تحميل بيانات الشحنة: {shipment.shipment_number}")

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"فشل في تحميل بيانات الشحنة:\n{str(e)}"
            )
        finally:
            session.close()

    def load_shipment_items(self, shipment_id):
        """تحميل أصناف الشحنة"""
        session = self.db_manager.get_session()
        try:
            items = session.query(ShipmentItem).filter(
                ShipmentItem.shipment_id == shipment_id
            ).all()

            self.items_table.setRowCount(len(items))
            for row, item in enumerate(items):
                self.items_table.setItem(row, 0, QTableWidgetItem(item.item.name if item.item else ""))
                self.items_table.setItem(row, 1, QTableWidgetItem(str(item.quantity)))
                self.items_table.setItem(row, 2, QTableWidgetItem(format_number(item.unit_price, 2, True)))
                self.items_table.setItem(row, 3, QTableWidgetItem(format_number(item.total_price, 2, True)))
                self.items_table.setItem(row, 4, QTableWidgetItem(format_number(item.weight, 2, True)))
                self.items_table.setItem(row, 5, QTableWidgetItem(item.notes or ""))

                # حفظ معرف الصنف
                self.items_table.item(row, 0).setData(Qt.UserRole, item.item_id)

            self.update_items_totals()

        except Exception as e:
            print(f"خطأ في تحميل الأصناف: {str(e)}")
        finally:
            session.close()

    def load_shipment_containers(self, shipment_id):
        """تحميل حاويات الشحنة"""
        session = self.db_manager.get_session()
        try:
            containers = session.query(Container).filter(
                Container.shipment_id == shipment_id
            ).all()

            self.containers_table.setRowCount(len(containers))
            for row, container in enumerate(containers):
                self.containers_table.setItem(row, 0, QTableWidgetItem(container.container_number or ""))
                self.containers_table.setItem(row, 1, QTableWidgetItem(container.container_type or ""))
                self.containers_table.setItem(row, 2, QTableWidgetItem(container.container_size or ""))
                self.containers_table.setItem(row, 3, QTableWidgetItem(f"{container.weight_empty:.2f}" if container.weight_empty else "0.00"))
                self.containers_table.setItem(row, 4, QTableWidgetItem(f"{container.weight_loaded:.2f}" if container.weight_loaded else "0.00"))
                self.containers_table.setItem(row, 5, QTableWidgetItem(container.status or ""))
                self.containers_table.setItem(row, 6, QTableWidgetItem(container.notes or ""))

                # حفظ معرف الحاوية
                self.containers_table.item(row, 0).setData(Qt.UserRole, container.id)

        except Exception as e:
            print(f"خطأ في تحميل الحاويات: {str(e)}")
        finally:
            session.close()

    def fill_test_data(self):
        """ملء النموذج ببيانات تجريبية من واقع النظام"""
        from PySide6.QtCore import QDate

        try:
            # ملء بيانات المورد من النظام
            self.fill_real_supplier_data()

            # البيانات الأساسية - توليد رقم شحنة فريد
            unique_number = self.generate_unique_shipment_number()
            self.shipment_number_edit.setText(unique_number)
            self.supplier_invoice_edit.setText("INV-2025-12345")
            self.shipment_status_combo.setCurrentText("قيد الشحن")
            self.clearance_status_combo.setCurrentText("في الانتظار")
            self.tracking_number_edit.setText("TRK123456789")
            self.bill_of_lading_edit.setText("BOL-2025-001")
            self.notes_edit.setText("شحنة تجريبية للاختبار - تحتوي على مواد إلكترونية")

            # البيانات المالية
            self.currency_combo.setCurrentText("USD")
            self.exchange_rate_edit.setText("3.67")
            self.goods_value_edit.setText("50000.00")
            self.shipping_cost_edit.setText("2500.00")
            if hasattr(self, 'insurance_edit'):
                self.insurance_edit.setText("500.00")
            elif hasattr(self, 'insurance_cost_edit'):
                self.insurance_cost_edit.setText("500.00")
            self.customs_fees_edit.setText("1200.00")
            self.other_fees_edit.setText("300.00")
            self.payment_status_combo.setCurrentText("مدفوع جزئياً")
            self.paid_amount_edit.setText("30000.00")
            self.payment_date_edit.setDate(QDate.currentDate())
            self.payment_method_combo.setCurrentText("تحويل بنكي")

            # بيانات الشحن
            self.shipping_company_edit.setText("شركة الخليج للشحن")
            self.shipping_type_combo.setCurrentText("بحري")
            self.shipping_method_combo.setCurrentText("FCL")
            self.delivery_terms_combo.setCurrentText("FOB")
            self.departure_port_edit.setText("ميناء جبل علي")
            self.arrival_port_edit.setText("ميناء الملك عبدالعزيز")
            self.bill_of_lading_number_edit.setText("BL-2024-001234")
            self.main_container_number_edit.setText("MSKU1234567")
            self.final_destination_edit.setText("الرياض - المملكة العربية السعودية")
            self.vessel_name_edit.setText("سفينة الخليج 1")
            self.voyage_number_edit.setText("V2025-001")
            self.bl_number_edit.setText("BL-2025-12345")
            self.departure_date_edit.setDate(QDate.currentDate().addDays(-5))
            self.arrival_date_edit.setDate(QDate.currentDate().addDays(10))
            self.estimated_departure_edit.setDate(QDate.currentDate().addDays(-5))
            self.estimated_arrival_edit.setDate(QDate.currentDate().addDays(10))

            # إضافة بعض الأصناف التجريبية من النظام
            self.add_real_test_items()

            # إضافة حاوية تجريبية
            self.add_test_container()

            # عرض إحصائيات البيانات المستخدمة
            self.show_data_statistics()

            QMessageBox.information(self, "بيانات تجريبية", "✅ تم ملء النموذج ببيانات تجريبية من واقع النظام بنجاح!")

        except Exception as e:
            QMessageBox.warning(
                self,
                "تحذير",
                f"تم ملء البيانات الأساسية، لكن حدث خطأ في ملء بيانات النظام:\n{str(e)}\n\nسيتم استخدام البيانات الافتراضية."
            )
            # في حالة الخطأ، استخدم البيانات الافتراضية
            self.add_test_items()

    def fill_real_supplier_data(self):
        """ملء بيانات مورد حقيقي من النظام"""
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Supplier

            db_manager = DatabaseManager()
            session = db_manager.get_session()

            # الحصول على أول مورد نشط
            supplier = session.query(Supplier).filter(
                Supplier.is_active == True
            ).first()

            if supplier:
                # ملء حقل المورد بالاسم والكود
                supplier_text = f"{supplier.name} ({supplier.code})"
                self.supplier_edit.setText(supplier_text)

                # حفظ معرف المورد
                self.selected_supplier_id = supplier.id
                # تعيين معرف المورد كخاصية للحقل للتحقق من الصحة
                self.supplier_edit.setProperty("supplier_id", supplier.id)

                print(f"✅ تم ملء بيانات المورد: {supplier.name}")
            else:
                # في حالة عدم وجود موردين، استخدم بيانات افتراضية
                self.supplier_edit.setText("شركة الإمارات للتجارة")
                print("⚠️ لم يتم العثور على موردين في النظام، تم استخدام بيانات افتراضية")

        except Exception as e:
            print(f"خطأ في ملء بيانات المورد: {str(e)}")
            # في حالة الخطأ، استخدم بيانات افتراضية
            self.supplier_edit.setText("شركة الإمارات للتجارة")
        finally:
            if 'session' in locals():
                session.close()

    def add_real_test_items(self):
        """إضافة أصناف حقيقية من النظام"""
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Item

            db_manager = DatabaseManager()
            session = db_manager.get_session()

            # الحصول على أول 3 أصناف نشطة من النظام
            items = session.query(Item).filter(
                Item.is_active == True
            ).limit(3).all()

            if items:
                # مسح الجدول أولاً
                self.items_table.setRowCount(0)

                # إضافة الأصناف الحقيقية
                for i, item in enumerate(items):
                    row = self.items_table.rowCount()
                    self.items_table.insertRow(row)

                    # استخدام بيانات الصنف الحقيقية
                    quantity = 10 + (i * 5)  # كميات متنوعة
                    unit_price = item.selling_price if item.selling_price > 0 else (100.0 + (i * 50))
                    total_price = quantity * unit_price
                    weight = item.weight if item.weight else (1.0 + i)

                    # ملء البيانات
                    self.items_table.setItem(row, 0, QTableWidgetItem(item.code))  # كود الصنف
                    self.items_table.setItem(row, 1, QTableWidgetItem(item.name))  # اسم الصنف
                    self.items_table.setItem(row, 2, QTableWidgetItem(str(quantity)))  # الكمية
                    self.items_table.setItem(row, 3, QTableWidgetItem(format_number(unit_price, 2, True)))  # سعر الوحدة
                    self.items_table.setItem(row, 4, QTableWidgetItem(format_number(total_price, 2, True)))  # السعر الإجمالي
                    self.items_table.setItem(row, 5, QTableWidgetItem(format_number(weight, 2, True)))  # الوزن
                    self.items_table.setItem(row, 6, QTableWidgetItem(item.description or ""))  # ملاحظات

                # تحديث المجموع الكلي
                self.update_items_total()

                print(f"✅ تم إضافة {len(items)} صنف حقيقي من النظام")
            else:
                # في حالة عدم وجود أصناف، استخدم البيانات الافتراضية
                print("⚠️ لم يتم العثور على أصناف في النظام، سيتم استخدام البيانات الافتراضية")
                self.add_test_items()

        except Exception as e:
            print(f"خطأ في إضافة الأصناف الحقيقية: {str(e)}")
            # في حالة الخطأ، استخدم البيانات الافتراضية
            self.add_test_items()
        finally:
            if 'session' in locals():
                session.close()

    def show_data_statistics(self):
        """عرض إحصائيات البيانات المستخدمة"""
        try:
            from src.database.database_manager import DatabaseManager
            from src.database.models import Item, Supplier

            db_manager = DatabaseManager()
            session = db_manager.get_session()

            # إحصائيات الموردين
            total_suppliers = session.query(Supplier).count()
            active_suppliers = session.query(Supplier).filter(Supplier.is_active == True).count()

            # إحصائيات الأصناف
            total_items = session.query(Item).count()
            active_items = session.query(Item).filter(Item.is_active == True).count()

            # عرض الإحصائيات في وحدة التحكم
            print("📊 إحصائيات البيانات المستخدمة:")
            print("=" * 40)
            print(f"📦 إجمالي الموردين: {total_suppliers}")
            print(f"✅ الموردين النشطين: {active_suppliers}")
            print(f"📋 إجمالي الأصناف: {total_items}")
            print(f"✅ الأصناف النشطة: {active_items}")
            print("=" * 40)

        except Exception as e:
            print(f"خطأ في عرض الإحصائيات: {str(e)}")
        finally:
            if 'session' in locals():
                session.close()

    def add_test_items(self):
        """إضافة أصناف تجريبية"""
        test_items = [
            ["ELEC001", "لابتوب ديل XPS 13", "50", "800.00", "40000.00", "1.5", "لابتوبات للمكاتب"],
            ["ELEC002", "شاشة سامسونج 27 بوصة", "25", "300.00", "7500.00", "8.0", "شاشات عالية الدقة"],
            ["ELEC003", "طابعة HP LaserJet", "10", "250.00", "2500.00", "12.0", "طابعات ليزر"]
        ]

        self.items_table.setRowCount(len(test_items))
        for row, item in enumerate(test_items):
            for col, value in enumerate(item):
                from PySide6.QtWidgets import QTableWidgetItem
                self.items_table.setItem(row, col, QTableWidgetItem(str(value)))

    def add_test_container(self):
        """إضافة حاوية تجريبية"""
        test_containers = [
            ["CONT001", "20 قدم", "تم التحميل", "MSKU1234567", "15000", "تحتوي على معدات إلكترونية"]
        ]

        self.containers_table.setRowCount(len(test_containers))
        for row, container in enumerate(test_containers):
            for col, value in enumerate(container):
                from PySide6.QtWidgets import QTableWidgetItem
                self.containers_table.setItem(row, col, QTableWidgetItem(str(value)))
    
    def save_and_close(self):
        """حفظ وإغلاق النافذة"""
        if self.save_shipment():
            self.accept()
    
    def clear_form(self):
        """مسح جميع الحقول"""
        try:
            # تبويب البيانات الأساسية
            if hasattr(self, 'shipment_date_edit'):
                self.shipment_date_edit.setDate(QDate.currentDate())
            self.shipment_number_edit.clear()
            self.supplier_edit.clear()
            self.selected_supplier_id = None  # مسح معرف المورد المختار
            self.supplier_invoice_edit.clear()
            self.shipment_status_combo.setCurrentIndex(0)
            self.clearance_status_combo.setCurrentIndex(0)
            self.tracking_number_edit.clear()
            self.bill_of_lading_edit.clear()
            self.notes_edit.clear()

            # تبويب الأصناف
            self.items_table.setRowCount(0)
            self.total_items_label.setText("إجمالي الأصناف: 0")
            self.total_value_label.setText("إجمالي القيمة: 0.00")

            # تبويب المالية
            self.currency_combo.setCurrentIndex(0)
            self.exchange_rate_edit.setText("1.00")
            self.goods_value_edit.setText("0.00")
            self.shipping_cost_edit.setText("0.00")
            self.insurance_cost_edit.setText("0.00")
            self.customs_fees_edit.setText("0.00")
            self.other_fees_edit.setText("0.00")
            self.total_costs_edit.setText("0.00")
            self.total_local_currency_edit.setText("0.00")
            self.payment_status_combo.setCurrentIndex(0)
            self.paid_amount_edit.setText("0.00")
            self.remaining_amount_edit.setText("0.00")
            self.payment_date_edit.setDate(QDate.currentDate())
            self.payment_method_combo.setCurrentIndex(0)

            # تبويب الشحن
            self.shipping_company_edit.clear()
            self.shipping_type_combo.setCurrentIndex(0)
            self.shipping_method_combo.setCurrentIndex(0)
            self.incoterms_combo.setCurrentIndex(0)
            self.port_of_loading_edit.clear()
            self.port_of_discharge_edit.clear()
            self.final_destination_edit.clear()
            self.vessel_name_edit.clear()
            self.voyage_number_edit.clear()
            if hasattr(self, 'dhl_number_edit'):
                self.dhl_number_edit.clear()
            self.bill_of_lading_number_edit.clear()
            self.main_container_number_edit.clear()
            self.estimated_departure_date_edit.setDate(QDate.currentDate())
            self.actual_departure_date_edit.setDate(QDate.currentDate())
            self.estimated_arrival_date_edit.setDate(QDate.currentDate())
            self.actual_arrival_date_edit.setDate(QDate.currentDate())

            # تبويب الحاويات
            self.containers_table.setRowCount(0)
            self.total_weight_spin.setValue(0.00)
            self.total_volume_spin.setValue(0.00)
            self.packages_count_spin.setValue(0)
            self.packaging_type_combo.setCurrentIndex(0)
            self.shipping_notes_edit.clear()
            self.total_containers_label.setText("إجمالي الحاويات: 0")

            # تبويب المستندات
            self.initial_docs_edit.clear()
            self.dn_docs_edit.clear()
            self.customs_docs_edit.clear()
            self.bill_lading_edit.clear()
            self.items_images_edit.clear()
            self.other_docs_edit.clear()
            self.documents_table.setRowCount(0)
            self.total_documents_label.setText("إجمالي المستندات: 0")

            print("✅ تم مسح النموذج بالكامل")
        except Exception as e:
            print(f"خطأ في مسح النموذج: {str(e)}")

    def import_from_excel(self):
        """استيراد البيانات من ملف Excel"""
        try:
            from PySide6.QtWidgets import QFileDialog, QMessageBox
            import pandas as pd

            # اختيار ملف Excel
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف Excel",
                "",
                "Excel Files (*.xlsx *.xls);;All Files (*)"
            )

            if not file_path:
                return

            # قراءة ملف Excel
            try:
                df = pd.read_excel(file_path)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في قراءة ملف Excel:\n{str(e)}")
                return

            if df.empty:
                QMessageBox.warning(self, "تحذير", "ملف Excel فارغ!")
                return

            # عرض نافذة اختيار الأعمدة
            dialog = ExcelImportDialog(self, df)
            if dialog.exec() == QDialog.Accepted:
                mapping = dialog.get_column_mapping()
                self.apply_excel_data(df, mapping)
                QMessageBox.information(self, "نجح", "تم استيراد البيانات بنجاح!")

        except ImportError:
            QMessageBox.critical(self, "خطأ", "مكتبة pandas غير مثبتة!\nيرجى تثبيتها باستخدام: pip install pandas openpyxl")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الاستيراد:\n{str(e)}")

    def apply_excel_data(self, df, mapping):
        """تطبيق البيانات المستوردة من Excel"""
        try:
            if df.empty:
                return

            # أخذ أول صف من البيانات
            row = df.iloc[0]

            # تطبيق البيانات حسب التطابق
            for field_name, column_name in mapping.items():
                if column_name and column_name in df.columns:
                    value = row[column_name]
                    if pd.notna(value):  # تجاهل القيم الفارغة
                        self.set_field_value(field_name, str(value))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تطبيق البيانات:\n{str(e)}")

    def set_field_value(self, field_name, value):
        """تعيين قيمة الحقل"""
        try:
            # البيانات المالية
            if field_name == "exchange_rate" and hasattr(self, 'exchange_rate_edit'):
                self.exchange_rate_edit.setText(value)
            elif field_name == "goods_value" and hasattr(self, 'goods_value_edit'):
                self.goods_value_edit.setText(value)
            elif field_name == "shipping_cost" and hasattr(self, 'shipping_cost_edit'):
                self.shipping_cost_edit.setText(value)
            elif field_name == "insurance_cost" and hasattr(self, 'insurance_cost_edit'):
                self.insurance_cost_edit.setText(value)
            elif field_name == "customs_fees" and hasattr(self, 'customs_fees_edit'):
                self.customs_fees_edit.setText(value)
            elif field_name == "other_fees" and hasattr(self, 'other_fees_edit'):
                self.other_fees_edit.setText(value)
            elif field_name == "paid_amount" and hasattr(self, 'paid_amount_edit'):
                self.paid_amount_edit.setText(value)

            # بيانات الشحن
            elif field_name == "shipping_company" and hasattr(self, 'shipping_company_edit'):
                self.shipping_company_edit.setText(value)
            elif field_name == "arrival_port" and hasattr(self, 'arrival_port_edit'):
                self.arrival_port_edit.setText(value)
            elif field_name == "dhl_number" and hasattr(self, 'dhl_number_edit'):
                self.dhl_number_edit.setText(value)
            elif field_name == "expected_arrival_date" and hasattr(self, 'expected_arrival_date_edit'):
                try:
                    from PySide6.QtCore import QDate
                    import pandas as pd
                    # محاولة تحويل التاريخ
                    date_obj = pd.to_datetime(value).date()
                    qdate = QDate(date_obj.year, date_obj.month, date_obj.day)
                    self.expected_arrival_date_edit.setDate(qdate)
                except:
                    pass  # تجاهل في حالة فشل تحويل التاريخ

            # رقم الحاوية (من تبويب الحاويات)
            elif field_name == "container_number":
                # إضافة حاوية جديدة إذا لم تكن موجودة
                if hasattr(self, 'containers_table') and self.containers_table.rowCount() == 0:
                    self.add_container()
                    if self.containers_table.rowCount() > 0:
                        from PySide6.QtWidgets import QTableWidgetItem
                        item = QTableWidgetItem(value)
                        self.containers_table.setItem(0, 0, item)

        except Exception as e:
            print(f"خطأ في تعيين قيمة الحقل {field_name}: {str(e)}")


def main():
    """اختبار النافذة النهائية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = NewShipmentWindow()
    window.show()
    
    print("✅ تم فتح النافذة النهائية مع أزرار التحكم")
    print("🔍 تحقق من وجود:")
    print("   • عنوان أزرق في الأعلى")
    print("   • شريط أزرار ملون: إضافة (أزرق)، حفظ (أخضر)، تعديل (برتقالي)، خروج (أحمر)")
    print("   • تبويبات: البيانات الأساسية، الأصناف، البيانات المالية")
    print("   • أزرار حفظ وإغلاق/إلغاء في الأسفل")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
