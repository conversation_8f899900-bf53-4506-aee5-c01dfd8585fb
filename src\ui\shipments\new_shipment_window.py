# -*- coding: utf-8 -*-
"""
نافذة شحنة جديدة
New Shipment Window
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTabWidget, QWidget,
                               QComboBox, QTextEdit, QMessageBox, QGroupBox,
                               QDateEdit, QDoubleSpinBox, QCheckBox, QSpacerItem,
                               QSizePolicy, QTableWidget, QTableWidgetItem, QHeaderView,
                               QAbstractItemView, QSpinBox, QFileDialog, QGridLayout)
from PySide6.QtCore import Qt, QDate, Signal, QUrl
from PySide6.QtGui import QKeySequence, QShortcut, QDesktopServices
import os

from ...database.database_manager import DatabaseManager
from ...database.models import Shipment, Supplier, Currency, ShipmentItem, Item, Container, ShipmentDocument
from .supplier_search_dialog import SupplierSearchDialog
from .item_search_dialog import ItemSearchDialog
from .container_dialog import ContainerDialog
from .add_link_dialog import AddLinkDialog
from .attachments_manager_dialog import AttachmentsManagerDialog

class NewShipmentWindow(QDialog):
    """نافذة شحنة جديدة"""
    
    shipment_saved = Signal(int)  # إشارة عند حفظ الشحنة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.current_shipment_id = None
        self.setup_ui()
        self.setup_connections()
        self.load_initial_data()

        # التأكد من إظهار الأزرار
        self.ensure_buttons_visible()

    def ensure_buttons_visible(self):
        """التأكد من أن أزرار التحكم مرئية"""
        try:
            if hasattr(self, 'new_button'):
                self.new_button.setVisible(True)
                self.new_button.show()
                self.new_button.raise_()

            if hasattr(self, 'save_button'):
                self.save_button.setVisible(True)
                self.save_button.show()
                self.save_button.raise_()

            if hasattr(self, 'edit_button'):
                self.edit_button.setVisible(True)
                self.edit_button.show()
                self.edit_button.raise_()

            if hasattr(self, 'exit_button'):
                self.exit_button.setVisible(True)
                self.exit_button.show()
                self.exit_button.raise_()

            # فرض تحديث النافذة
            self.update()
            self.repaint()

        except Exception as e:
            print(f"خطأ في إظهار الأزرار: {str(e)}")

    def create_control_buttons_simple(self, main_layout):
        """إنشاء أزرار التحكم بطريقة بسيطة"""
        # تخطيط أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(20, 15, 20, 15)

        # زر إضافة شحنة جديدة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setMinimumSize(120, 45)
        self.new_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setMinimumSize(120, 45)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setMinimumSize(120, 45)
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.edit_button.setEnabled(False)

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setMinimumSize(120, 45)
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addWidget(self.new_button)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_button)

        # إضافة تخطيط الأزرار للتخطيط الرئيسي
        main_layout.addLayout(buttons_layout)

    def create_visible_control_buttons(self, main_layout):
        """إنشاء أزرار التحكم المرئية - حل نهائي"""
        # إنشاء مجموعة أزرار بارزة
        from PySide6.QtWidgets import QFrame

        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.Box | QFrame.Raised)
        buttons_frame.setLineWidth(2)
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
        """)

        frame_layout = QHBoxLayout(buttons_frame)
        frame_layout.setSpacing(20)
        frame_layout.setContentsMargins(20, 15, 20, 15)

        # زر إضافة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setFixedSize(140, 50)
        self.new_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #2980b9;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                border: 3px solid #1f5f8b;
            }
            QPushButton:pressed {
                background-color: #1f5f8b;
            }
        """)

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setFixedSize(140, 50)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #229954;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
                border: 3px solid #1e8449;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setFixedSize(140, 50)
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #e67e22;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #e67e22;
                border: 3px solid #d35400;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
                border: 3px solid #95a5a6;
            }
        """)
        self.edit_button.setEnabled(False)

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setFixedSize(140, 50)
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #c0392b;
                border-radius: 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 3px solid #a93226;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)

        # إضافة الأزرار للإطار
        frame_layout.addWidget(self.new_button)
        frame_layout.addWidget(self.save_button)
        frame_layout.addWidget(self.edit_button)
        frame_layout.addStretch()
        frame_layout.addWidget(self.exit_button)

        # إضافة الإطار للتخطيط الرئيسي
        main_layout.addWidget(buttons_frame)

        # فرض الظهور
        buttons_frame.show()
        self.new_button.show()
        self.save_button.show()
        self.edit_button.show()
        self.exit_button.show()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("شحنة جديدة")
        self.setModal(True)
        self.resize(900, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # إنشاء أزرار التحكم أولاً
        self.create_visible_control_buttons(main_layout)

        # إنشاء التبويبات
        self.create_basic_tab()
        self.create_items_tab()
        self.create_financial_tab()
        self.create_shipping_tab()
        self.create_containers_tab()
        self.create_documents_tab()

    def create_control_buttons(self, main_layout):
        """إنشاء أزرار التحكم"""
        # تخطيط أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر إضافة شحنة جديدة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.edit_button.setEnabled(False)  # معطل في البداية

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addWidget(self.new_button)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.exit_button)

        # إضافة تخطيط الأزرار للتخطيط الرئيسي
        main_layout.addLayout(buttons_layout)

        # التأكد من أن الأزرار مرئية
        self.new_button.setVisible(True)
        self.save_button.setVisible(True)
        self.edit_button.setVisible(True)
        self.exit_button.setVisible(True)

        # فرض تحديث النافذة
        self.update()
        self.repaint()

    def create_basic_tab(self):
        """إنشاء التبويب الأساسي"""
        basic_tab = QWidget()
        self.tab_widget.addTab(basic_tab, "البيانات الأساسية")
        
        layout = QVBoxLayout(basic_tab)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        
        # رقم الشحنة (تسلسل آلي)
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setReadOnly(True)
        self.shipment_number_edit.setStyleSheet("background-color: #f0f0f0;")
        basic_layout.addRow("رقم الشحنة:", self.shipment_number_edit)
        
        # المورد مع زر البحث
        supplier_layout = QHBoxLayout()
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setReadOnly(True)
        self.supplier_search_button = QPushButton("بحث (F9)")
        self.supplier_search_button.setMaximumWidth(100)
        supplier_layout.addWidget(self.supplier_edit)
        supplier_layout.addWidget(self.supplier_search_button)
        basic_layout.addRow("المورد:", supplier_layout)
        
        # رقم فاتورة المورد
        self.supplier_invoice_edit = QLineEdit()
        basic_layout.addRow("رقم فاتورة المورد:", self.supplier_invoice_edit)
        
        # حالة الشحنة
        self.shipment_status_combo = QComboBox()
        self.shipment_status_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق", 
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        basic_layout.addRow("حالة الشحنة:", self.shipment_status_combo)
        
        # حالة الإفراج
        self.clearance_status_combo = QComboBox()
        self.clearance_status_combo.addItems(["بدون الافراج", "مع الافراج"])
        basic_layout.addRow("حالة الإفراج:", self.clearance_status_combo)
        
        layout.addWidget(basic_group)
        
        # مجموعة بيانات إضافية
        additional_group = QGroupBox("بيانات إضافية")
        additional_layout = QFormLayout(additional_group)
        
        # رقم التتبع
        self.tracking_number_edit = QLineEdit()
        additional_layout.addRow("رقم التتبع:", self.tracking_number_edit)
        
        # بوليصة الشحن
        self.bill_of_lading_edit = QLineEdit()
        additional_layout.addRow("بوليصة الشحن:", self.bill_of_lading_edit)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        additional_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addWidget(additional_group)
        layout.addStretch()
        
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        items_tab = QWidget()
        self.tab_widget.addTab(items_tab, "الأصناف")

        layout = QVBoxLayout(items_tab)

        # أزرار إدارة الأصناف
        buttons_layout = QHBoxLayout()

        self.add_item_button = QPushButton("إضافة صنف (F9)")
        self.add_item_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")

        self.remove_item_button = QPushButton("حذف صنف")
        self.remove_item_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        self.remove_item_button.setEnabled(False)

        self.edit_item_button = QPushButton("تعديل صنف")
        self.edit_item_button.setEnabled(False)

        buttons_layout.addWidget(self.add_item_button)
        buttons_layout.addWidget(self.remove_item_button)
        buttons_layout.addWidget(self.edit_item_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة",
            "السعر الإجمالي", "الوزن", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # كود الصنف
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الصنف
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # سعر الوحدة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # السعر الإجمالي
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الوزن
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # ملاحظات

        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)

        layout.addWidget(self.items_table)

        # إجمالي الأصناف
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_items_label = QLabel("إجمالي الأصناف: 0")
        self.total_items_label.setStyleSheet("font-weight: bold; font-size: 14px;")

        self.total_amount_label = QLabel("إجمالي المبلغ: 0.00")
        self.total_amount_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2196F3;")

        totals_layout.addWidget(self.total_items_label)
        totals_layout.addWidget(QLabel("  |  "))
        totals_layout.addWidget(self.total_amount_label)

        layout.addLayout(totals_layout)
        
    def create_financial_tab(self):
        """إنشاء تبويب المالية"""
        financial_tab = QWidget()
        self.tab_widget.addTab(financial_tab, "المالية")

        layout = QVBoxLayout(financial_tab)

        # مجموعة العملة والأسعار
        currency_group = QGroupBox("العملة والأسعار")
        currency_layout = QFormLayout(currency_group)

        # العملة
        self.currency_combo = QComboBox()
        self.load_currencies()
        currency_layout.addRow("العملة:", self.currency_combo)

        # سعر الصرف
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.01, 999999.99)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0000)
        currency_layout.addRow("سعر الصرف:", self.exchange_rate_spin)

        layout.addWidget(currency_group)

        # مجموعة التكاليف
        costs_group = QGroupBox("التكاليف")
        costs_layout = QFormLayout(costs_group)

        # قيمة البضاعة
        self.goods_value_spin = QDoubleSpinBox()
        self.goods_value_spin.setRange(0.00, 999999999.99)
        self.goods_value_spin.setDecimals(2)
        self.goods_value_spin.setReadOnly(True)  # محسوبة تلقائياً من الأصناف
        self.goods_value_spin.setStyleSheet("background-color: #f0f0f0;")
        costs_layout.addRow("قيمة البضاعة:", self.goods_value_spin)

        # تكلفة الشحن
        self.shipping_cost_spin = QDoubleSpinBox()
        self.shipping_cost_spin.setRange(0.00, 999999999.99)
        self.shipping_cost_spin.setDecimals(2)
        costs_layout.addRow("تكلفة الشحن:", self.shipping_cost_spin)

        # تكلفة التأمين
        self.insurance_cost_spin = QDoubleSpinBox()
        self.insurance_cost_spin.setRange(0.00, 999999999.99)
        self.insurance_cost_spin.setDecimals(2)
        costs_layout.addRow("تكلفة التأمين:", self.insurance_cost_spin)

        # رسوم الجمارك
        self.customs_fees_spin = QDoubleSpinBox()
        self.customs_fees_spin.setRange(0.00, 999999999.99)
        self.customs_fees_spin.setDecimals(2)
        costs_layout.addRow("رسوم الجمارك:", self.customs_fees_spin)

        # رسوم أخرى
        self.other_fees_spin = QDoubleSpinBox()
        self.other_fees_spin.setRange(0.00, 999999999.99)
        self.other_fees_spin.setDecimals(2)
        costs_layout.addRow("رسوم أخرى:", self.other_fees_spin)

        layout.addWidget(costs_group)

        # مجموعة الإجماليات
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QFormLayout(totals_group)

        # إجمالي التكاليف
        self.total_costs_spin = QDoubleSpinBox()
        self.total_costs_spin.setRange(0.00, 999999999.99)
        self.total_costs_spin.setDecimals(2)
        self.total_costs_spin.setReadOnly(True)
        self.total_costs_spin.setStyleSheet("background-color: #e3f2fd; font-weight: bold;")
        totals_layout.addRow("إجمالي التكاليف:", self.total_costs_spin)

        # إجمالي المبلغ بالعملة المحلية
        self.total_local_currency_spin = QDoubleSpinBox()
        self.total_local_currency_spin.setRange(0.00, 999999999.99)
        self.total_local_currency_spin.setDecimals(2)
        self.total_local_currency_spin.setReadOnly(True)
        self.total_local_currency_spin.setStyleSheet("background-color: #e8f5e8; font-weight: bold;")
        totals_layout.addRow("إجمالي المبلغ (ريال):", self.total_local_currency_spin)

        layout.addWidget(totals_group)

        # مجموعة الدفع
        payment_group = QGroupBox("معلومات الدفع")
        payment_layout = QFormLayout(payment_group)

        # حالة الدفع
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItems([
            "لم يتم الدفع", "دفع جزئي", "تم الدفع بالكامل", "مؤجل"
        ])
        payment_layout.addRow("حالة الدفع:", self.payment_status_combo)

        # المبلغ المدفوع
        self.paid_amount_spin = QDoubleSpinBox()
        self.paid_amount_spin.setRange(0.00, 999999999.99)
        self.paid_amount_spin.setDecimals(2)
        payment_layout.addRow("المبلغ المدفوع:", self.paid_amount_spin)

        # المبلغ المتبقي
        self.remaining_amount_spin = QDoubleSpinBox()
        self.remaining_amount_spin.setRange(0.00, 999999999.99)
        self.remaining_amount_spin.setDecimals(2)
        self.remaining_amount_spin.setReadOnly(True)
        self.remaining_amount_spin.setStyleSheet("background-color: #fff3e0; font-weight: bold;")
        payment_layout.addRow("المبلغ المتبقي:", self.remaining_amount_spin)

        # تاريخ الدفع
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        payment_layout.addRow("تاريخ الدفع:", self.payment_date_edit)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([
            "نقداً", "شيك", "تحويل بنكي", "بطاقة ائتمان", "اعتماد مستندي"
        ])
        payment_layout.addRow("طريقة الدفع:", self.payment_method_combo)

        layout.addWidget(payment_group)

        # ملاحظات مالية
        notes_group = QGroupBox("ملاحظات مالية")
        notes_layout = QVBoxLayout(notes_group)

        self.financial_notes_edit = QTextEdit()
        self.financial_notes_edit.setMaximumHeight(80)
        self.financial_notes_edit.setPlaceholderText("أدخل أي ملاحظات مالية إضافية...")
        notes_layout.addWidget(self.financial_notes_edit)

        layout.addWidget(notes_group)

        layout.addStretch()
        
    def create_shipping_tab(self):
        """إنشاء تبويب الشحن"""
        shipping_tab = QWidget()
        self.tab_widget.addTab(shipping_tab, "الشحن")

        layout = QVBoxLayout(shipping_tab)

        # مجموعة معلومات الشحن الأساسية
        basic_shipping_group = QGroupBox("معلومات الشحن الأساسية")
        basic_layout = QFormLayout(basic_shipping_group)

        # شركة الشحن
        self.shipping_company_edit = QLineEdit()
        self.shipping_company_edit.setPlaceholderText("اسم شركة الشحن...")
        basic_layout.addRow("شركة الشحن:", self.shipping_company_edit)

        # نوع الشحن
        self.shipping_type_combo = QComboBox()
        self.shipping_type_combo.addItems([
            "بحري", "جوي", "بري", "متعدد الوسائط"
        ])
        basic_layout.addRow("نوع الشحن:", self.shipping_type_combo)

        # طريقة الشحن
        self.shipping_method_combo = QComboBox()
        self.shipping_method_combo.addItems([
            "FCL (حاوية كاملة)", "LCL (حاوية مشتركة)", "شحن عادي", "شحن سريع"
        ])
        basic_layout.addRow("طريقة الشحن:", self.shipping_method_combo)

        # شروط التسليم (Incoterms)
        self.incoterms_combo = QComboBox()
        self.incoterms_combo.addItems([
            "EXW (Ex Works)", "FCA (Free Carrier)", "CPT (Carriage Paid To)",
            "CIP (Carriage and Insurance Paid)", "DAP (Delivered at Place)",
            "DPU (Delivered at Place Unloaded)", "DDP (Delivered Duty Paid)",
            "FAS (Free Alongside Ship)", "FOB (Free on Board)",
            "CFR (Cost and Freight)", "CIF (Cost, Insurance and Freight)"
        ])
        basic_layout.addRow("شروط التسليم:", self.incoterms_combo)

        layout.addWidget(basic_shipping_group)

        # مجموعة الموانئ والمسار
        ports_group = QGroupBox("الموانئ والمسار")
        ports_layout = QFormLayout(ports_group)

        # ميناء التحميل
        self.port_of_loading_edit = QLineEdit()
        self.port_of_loading_edit.setPlaceholderText("ميناء المغادرة...")
        ports_layout.addRow("ميناء التحميل:", self.port_of_loading_edit)

        # ميناء التفريغ
        self.port_of_discharge_edit = QLineEdit()
        self.port_of_discharge_edit.setPlaceholderText("ميناء الوصول...")
        ports_layout.addRow("ميناء التفريغ:", self.port_of_discharge_edit)

        # ميناء الوجهة النهائية
        self.final_destination_edit = QLineEdit()
        self.final_destination_edit.setPlaceholderText("الوجهة النهائية...")
        ports_layout.addRow("الوجهة النهائية:", self.final_destination_edit)

        layout.addWidget(ports_group)

        # مجموعة السفينة والرحلة
        vessel_group = QGroupBox("السفينة والرحلة")
        vessel_layout = QFormLayout(vessel_group)

        # اسم السفينة
        self.vessel_name_edit = QLineEdit()
        self.vessel_name_edit.setPlaceholderText("اسم السفينة أو الطائرة...")
        vessel_layout.addRow("اسم السفينة:", self.vessel_name_edit)

        # رقم الرحلة
        self.voyage_number_edit = QLineEdit()
        self.voyage_number_edit.setPlaceholderText("رقم الرحلة...")
        vessel_layout.addRow("رقم الرحلة:", self.voyage_number_edit)

        # رقم بوليصة الشحن
        self.bill_of_lading_number_edit = QLineEdit()
        self.bill_of_lading_number_edit.setPlaceholderText("رقم بوليصة الشحن...")
        vessel_layout.addRow("رقم بوليصة الشحن:", self.bill_of_lading_number_edit)

        layout.addWidget(vessel_group)

        # مجموعة التواريخ
        dates_group = QGroupBox("التواريخ المهمة")
        dates_layout = QFormLayout(dates_group)

        # تاريخ المغادرة المتوقع
        self.estimated_departure_date = QDateEdit()
        self.estimated_departure_date.setDate(QDate.currentDate())
        self.estimated_departure_date.setCalendarPopup(True)
        dates_layout.addRow("تاريخ المغادرة المتوقع:", self.estimated_departure_date)

        # تاريخ المغادرة الفعلي
        self.actual_departure_date = QDateEdit()
        self.actual_departure_date.setDate(QDate.currentDate())
        self.actual_departure_date.setCalendarPopup(True)
        dates_layout.addRow("تاريخ المغادرة الفعلي:", self.actual_departure_date)

        # تاريخ الوصول المتوقع
        self.estimated_arrival_date = QDateEdit()
        self.estimated_arrival_date.setDate(QDate.currentDate().addDays(30))
        self.estimated_arrival_date.setCalendarPopup(True)
        dates_layout.addRow("تاريخ الوصول المتوقع:", self.estimated_arrival_date)

        # تاريخ الوصول الفعلي
        self.actual_arrival_date = QDateEdit()
        self.actual_arrival_date.setDate(QDate.currentDate())
        self.actual_arrival_date.setCalendarPopup(True)
        dates_layout.addRow("تاريخ الوصول الفعلي:", self.actual_arrival_date)

        layout.addWidget(dates_group)

        # مجموعة معلومات إضافية
        additional_group = QGroupBox("معلومات إضافية")
        additional_layout = QFormLayout(additional_group)

        # وزن الشحنة الإجمالي
        self.total_weight_spin = QDoubleSpinBox()
        self.total_weight_spin.setRange(0.00, 999999.99)
        self.total_weight_spin.setDecimals(2)
        self.total_weight_spin.setSuffix(" كجم")
        additional_layout.addRow("الوزن الإجمالي:", self.total_weight_spin)

        # حجم الشحنة
        self.total_volume_spin = QDoubleSpinBox()
        self.total_volume_spin.setRange(0.00, 999999.99)
        self.total_volume_spin.setDecimals(2)
        self.total_volume_spin.setSuffix(" م³")
        additional_layout.addRow("الحجم الإجمالي:", self.total_volume_spin)

        # عدد الطرود
        self.packages_count_spin = QSpinBox()
        self.packages_count_spin.setRange(0, 999999)
        additional_layout.addRow("عدد الطرود:", self.packages_count_spin)

        # نوع التعبئة
        self.packaging_type_combo = QComboBox()
        self.packaging_type_combo.addItems([
            "صناديق كرتونية", "صناديق خشبية", "أكياس", "براميل",
            "حاويات", "منصات نقالة", "أخرى"
        ])
        additional_layout.addRow("نوع التعبئة:", self.packaging_type_combo)

        layout.addWidget(additional_group)

        # ملاحظات الشحن
        shipping_notes_group = QGroupBox("ملاحظات الشحن")
        shipping_notes_layout = QVBoxLayout(shipping_notes_group)

        self.shipping_notes_edit = QTextEdit()
        self.shipping_notes_edit.setMaximumHeight(80)
        self.shipping_notes_edit.setPlaceholderText("أدخل أي ملاحظات خاصة بالشحن...")
        shipping_notes_layout.addWidget(self.shipping_notes_edit)

        layout.addWidget(shipping_notes_group)

        layout.addStretch()
        
    def create_containers_tab(self):
        """إنشاء تبويب الحاويات"""
        containers_tab = QWidget()
        self.tab_widget.addTab(containers_tab, "الحاويات")

        layout = QVBoxLayout(containers_tab)

        # أزرار إدارة الحاويات
        buttons_layout = QHBoxLayout()

        self.add_container_button = QPushButton("إضافة حاوية")
        self.add_container_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")

        self.remove_container_button = QPushButton("حذف حاوية")
        self.remove_container_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        self.remove_container_button.setEnabled(False)

        self.edit_container_button = QPushButton("تعديل حاوية")
        self.edit_container_button.setEnabled(False)

        buttons_layout.addWidget(self.add_container_button)
        buttons_layout.addWidget(self.remove_container_button)
        buttons_layout.addWidget(self.edit_container_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الحاويات
        self.containers_table = QTableWidget()
        self.containers_table.setColumnCount(8)
        self.containers_table.setHorizontalHeaderLabels([
            "رقم الحاوية", "نوع الحاوية", "الحجم", "الوزن الفارغ",
            "الوزن المحمل", "الحالة", "رقم الختم", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.containers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحاوية
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # نوع الحاوية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الحجم
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الوزن الفارغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الوزن المحمل
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # رقم الختم
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # ملاحظات

        self.containers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.containers_table.setAlternatingRowColors(True)

        layout.addWidget(self.containers_table)

        # إجمالي الحاويات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_containers_label = QLabel("إجمالي الحاويات: 0")
        self.total_containers_label.setStyleSheet("font-weight: bold; font-size: 14px;")

        self.total_container_weight_label = QLabel("إجمالي الوزن: 0.00 كجم")
        self.total_container_weight_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2196F3;")

        totals_layout.addWidget(self.total_containers_label)
        totals_layout.addWidget(QLabel("  |  "))
        totals_layout.addWidget(self.total_container_weight_label)

        layout.addLayout(totals_layout)
        
    def create_documents_tab(self):
        """إنشاء تبويب المستندات"""
        documents_tab = QWidget()
        self.tab_widget.addTab(documents_tab, "المستندات")

        layout = QVBoxLayout(documents_tab)

        # قسم روابط المستندات المحددة
        documents_links_group = QGroupBox("روابط المستندات")
        documents_links_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        documents_links_layout = QGridLayout(documents_links_group)

        # المستندات الأولية
        self.initial_docs_label = QLabel("المستندات الأولية:")
        self.initial_docs_edit = QLineEdit()
        self.initial_docs_edit.setPlaceholderText("رابط المستندات الأولية")
        self.initial_docs_edit.setReadOnly(True)
        self.initial_docs_button = QPushButton("إضافة رابط")
        self.initial_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; }")

        self.initial_docs_attach_button = QPushButton("إضافة مرفق")
        self.initial_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; }")

        documents_links_layout.addWidget(self.initial_docs_label, 0, 0)
        documents_links_layout.addWidget(self.initial_docs_edit, 0, 1)
        documents_links_layout.addWidget(self.initial_docs_button, 0, 2)
        documents_links_layout.addWidget(self.initial_docs_attach_button, 0, 3)

        # مستندات DN
        self.dn_docs_label = QLabel("المستندات (DN):")
        self.dn_docs_edit = QLineEdit()
        self.dn_docs_edit.setPlaceholderText("رابط مستندات DN")
        self.dn_docs_edit.setReadOnly(True)
        self.dn_docs_button = QPushButton("إضافة رابط")
        self.dn_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; }")

        self.dn_docs_attach_button = QPushButton("إضافة مرفق")
        self.dn_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; }")

        documents_links_layout.addWidget(self.dn_docs_label, 1, 0)
        documents_links_layout.addWidget(self.dn_docs_edit, 1, 1)
        documents_links_layout.addWidget(self.dn_docs_button, 1, 2)
        documents_links_layout.addWidget(self.dn_docs_attach_button, 1, 3)

        # المستندات المرسلة للجمارك
        self.customs_docs_label = QLabel("المستندات المرسلة للجمارك:")
        self.customs_docs_edit = QLineEdit()
        self.customs_docs_edit.setPlaceholderText("رابط المستندات المرسلة للجمارك")
        self.customs_docs_edit.setReadOnly(True)
        self.customs_docs_button = QPushButton("إضافة رابط")
        self.customs_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; }")

        self.customs_docs_attach_button = QPushButton("إضافة مرفق")
        self.customs_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; }")

        documents_links_layout.addWidget(self.customs_docs_label, 2, 0)
        documents_links_layout.addWidget(self.customs_docs_edit, 2, 1)
        documents_links_layout.addWidget(self.customs_docs_button, 2, 2)
        documents_links_layout.addWidget(self.customs_docs_attach_button, 2, 3)

        # بوليصة الشحن
        self.bill_lading_label = QLabel("بوليصة الشحن:")
        self.bill_lading_edit = QLineEdit()
        self.bill_lading_edit.setPlaceholderText("رابط بوليصة الشحن")
        self.bill_lading_edit.setReadOnly(True)
        self.bill_lading_button = QPushButton("إضافة رابط")
        self.bill_lading_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; }")

        self.bill_lading_attach_button = QPushButton("إضافة مرفق")
        self.bill_lading_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; }")

        documents_links_layout.addWidget(self.bill_lading_label, 3, 0)
        documents_links_layout.addWidget(self.bill_lading_edit, 3, 1)
        documents_links_layout.addWidget(self.bill_lading_button, 3, 2)
        documents_links_layout.addWidget(self.bill_lading_attach_button, 3, 3)

        # صور الأصناف
        self.items_images_label = QLabel("صور الأصناف:")
        self.items_images_edit = QLineEdit()
        self.items_images_edit.setPlaceholderText("رابط صور الأصناف")
        self.items_images_edit.setReadOnly(True)
        self.items_images_button = QPushButton("إضافة رابط")
        self.items_images_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; }")

        self.items_images_attach_button = QPushButton("إضافة مرفق")
        self.items_images_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; }")

        documents_links_layout.addWidget(self.items_images_label, 4, 0)
        documents_links_layout.addWidget(self.items_images_edit, 4, 1)
        documents_links_layout.addWidget(self.items_images_button, 4, 2)
        documents_links_layout.addWidget(self.items_images_attach_button, 4, 3)

        # مستندات أخرى
        self.other_docs_label = QLabel("مستندات أخرى:")
        self.other_docs_edit = QLineEdit()
        self.other_docs_edit.setPlaceholderText("رابط مستندات أخرى")
        self.other_docs_edit.setReadOnly(True)
        self.other_docs_button = QPushButton("إضافة رابط")
        self.other_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; }")

        self.other_docs_attach_button = QPushButton("إضافة مرفق")
        self.other_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; }")

        documents_links_layout.addWidget(self.other_docs_label, 5, 0)
        documents_links_layout.addWidget(self.other_docs_edit, 5, 1)
        documents_links_layout.addWidget(self.other_docs_button, 5, 2)
        documents_links_layout.addWidget(self.other_docs_attach_button, 5, 3)

        layout.addWidget(documents_links_group)

        # قسم المستندات الإضافية (الجدول القديم)
        additional_docs_group = QGroupBox("مستندات إضافية")
        additional_docs_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        additional_docs_layout = QVBoxLayout(additional_docs_group)

        # أزرار إدارة المستندات الإضافية
        buttons_layout = QHBoxLayout()

        self.add_document_button = QPushButton("إضافة مستند")
        self.add_document_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")

        self.add_link_button = QPushButton("إضافة رابط")
        self.add_link_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; }")

        self.remove_document_button = QPushButton("حذف")
        self.remove_document_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        self.remove_document_button.setEnabled(False)

        self.open_document_button = QPushButton("فتح")
        self.open_document_button.setEnabled(False)

        self.add_attachment_button = QPushButton("إضافة مرفق")
        self.add_attachment_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; }")

        buttons_layout.addWidget(self.add_document_button)
        buttons_layout.addWidget(self.add_link_button)
        buttons_layout.addWidget(self.add_attachment_button)
        buttons_layout.addWidget(self.remove_document_button)
        buttons_layout.addWidget(self.open_document_button)
        buttons_layout.addStretch()

        additional_docs_layout.addLayout(buttons_layout)

        # جدول المستندات
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels([
            "اسم المستند", "النوع", "المسار/الرابط", "تاريخ الإضافة", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.documents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # اسم المستند
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المسار/الرابط
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # تاريخ الإضافة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # ملاحظات

        self.documents_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.documents_table.setAlternatingRowColors(True)

        additional_docs_layout.addWidget(self.documents_table)

        # إجمالي المستندات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_documents_label = QLabel("إجمالي المستندات: 0")
        self.total_documents_label.setStyleSheet("font-weight: bold; font-size: 14px;")

        totals_layout.addWidget(self.total_documents_label)

        additional_docs_layout.addLayout(totals_layout)

        layout.addWidget(additional_docs_group)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # أزرار التحكم الرئيسية
        self.new_button.clicked.connect(self.new_shipment)
        self.save_button.clicked.connect(self.save_shipment)
        self.edit_button.clicked.connect(self.edit_shipment)
        self.exit_button.clicked.connect(self.reject)
        self.supplier_search_button.clicked.connect(self.search_supplier)
        
        # اختصار F9 للبحث عن المورد
        f9_shortcut = QShortcut(QKeySequence("F9"), self)
        f9_shortcut.activated.connect(self.search_supplier)

        # اتصالات تبويب الأصناف
        self.add_item_button.clicked.connect(self.add_item)
        self.remove_item_button.clicked.connect(self.remove_item)
        self.edit_item_button.clicked.connect(self.edit_item)
        self.items_table.itemSelectionChanged.connect(self.on_item_selection_changed)
        self.items_table.itemChanged.connect(self.on_item_data_changed)

        # اتصالات تبويب المالية
        self.shipping_cost_spin.valueChanged.connect(self.calculate_totals)
        self.insurance_cost_spin.valueChanged.connect(self.calculate_totals)
        self.customs_fees_spin.valueChanged.connect(self.calculate_totals)
        self.other_fees_spin.valueChanged.connect(self.calculate_totals)
        self.exchange_rate_spin.valueChanged.connect(self.calculate_totals)
        self.paid_amount_spin.valueChanged.connect(self.calculate_remaining_amount)

        # اتصالات تبويب الحاويات
        self.add_container_button.clicked.connect(self.add_container)
        self.remove_container_button.clicked.connect(self.remove_container)
        self.edit_container_button.clicked.connect(self.edit_container)
        self.containers_table.itemSelectionChanged.connect(self.on_container_selection_changed)

        # اتصالات تبويب المستندات
        self.add_document_button.clicked.connect(self.add_document)
        self.add_link_button.clicked.connect(self.add_link)
        self.add_attachment_button.clicked.connect(self.add_attachment)
        self.remove_document_button.clicked.connect(self.remove_document)
        self.open_document_button.clicked.connect(self.open_document)
        self.documents_table.itemSelectionChanged.connect(self.on_document_selection_changed)
        self.documents_table.itemDoubleClicked.connect(self.open_document)

        # اتصالات أزرار روابط المستندات المحددة
        self.initial_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات الأولية", self.initial_docs_edit))
        self.dn_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات (DN)", self.dn_docs_edit))
        self.customs_docs_button.clicked.connect(lambda: self.add_specific_link("المستندات المرسلة للجمارك", self.customs_docs_edit))
        self.bill_lading_button.clicked.connect(lambda: self.add_specific_link("بوليصة الشحن", self.bill_lading_edit))
        self.items_images_button.clicked.connect(lambda: self.add_specific_link("صور الأصناف", self.items_images_edit))
        self.other_docs_button.clicked.connect(lambda: self.add_specific_link("مستندات أخرى", self.other_docs_edit))

        # اتصالات أزرار المرفقات للمستندات المحددة
        self.initial_docs_attach_button.clicked.connect(lambda: self.manage_attachments("المستندات الأولية", "initial_documents_files"))
        self.dn_docs_attach_button.clicked.connect(lambda: self.manage_attachments("المستندات (DN)", "dn_documents_files"))
        self.customs_docs_attach_button.clicked.connect(lambda: self.manage_attachments("المستندات المرسلة للجمارك", "customs_documents_files"))
        self.bill_lading_attach_button.clicked.connect(lambda: self.manage_attachments("بوليصة الشحن", "bill_of_lading_files"))
        self.items_images_attach_button.clicked.connect(lambda: self.manage_attachments("صور الأصناف", "items_images_files"))
        self.other_docs_attach_button.clicked.connect(lambda: self.manage_attachments("مستندات أخرى", "other_documents_files"))
        
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        # توليد رقم الشحنة التلقائي
        self.generate_shipment_number()
        # تحميل العملات
        self.load_currencies()
        
    def generate_shipment_number(self):
        """توليد رقم الشحنة التلقائي"""
        session = self.db_manager.get_session()
        try:
            # الحصول على آخر رقم شحنة
            last_shipment = session.query(Shipment).order_by(Shipment.id.desc()).first()
            if last_shipment:
                # استخراج الرقم من آخر شحنة وزيادته
                try:
                    last_number = int(last_shipment.shipment_number.split('-')[-1])
                    new_number = f"SH-{last_number + 1:06d}"
                except:
                    new_number = "SH-000001"
            else:
                new_number = "SH-000001"
                
            self.shipment_number_edit.setText(new_number)
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في توليد رقم الشحنة: {str(e)}")
            self.shipment_number_edit.setText("SH-000001")
        finally:
            session.close()
            
    def search_supplier(self):
        """البحث عن مورد"""
        dialog = SupplierSearchDialog(self)
        if dialog.exec() == QDialog.Accepted:
            supplier = dialog.get_selected_supplier()
            if supplier:
                self.supplier_edit.setText(f"{supplier.code} - {supplier.name}")
                self.supplier_edit.setProperty("supplier_id", supplier.id)
                
    def save_shipment(self):
        """حفظ الشحنة"""
        if not self.validate_data():
            return

        # الحصول على أصناف الشحنة
        shipment_items = self.get_shipment_items()
        if shipment_items is None:
            return

        session = self.db_manager.get_session()
        try:
            # حساب إجمالي المبلغ
            total_amount = sum(item['total_price'] for item in shipment_items)

            # إنشاء الشحنة الجديدة
            new_shipment = Shipment(
                shipment_number=self.shipment_number_edit.text(),
                supplier_id=self.supplier_edit.property("supplier_id"),
                supplier_invoice_number=self.supplier_invoice_edit.text(),
                shipment_status=self.shipment_status_combo.currentText(),
                clearance_status=self.clearance_status_combo.currentText(),
                tracking_number=self.tracking_number_edit.text(),
                bill_of_lading=self.bill_of_lading_edit.text(),
                notes=self.notes_edit.toPlainText(),
                total_amount=total_amount,
                # البيانات المالية
                currency_id=self.currency_combo.currentData(),
                exchange_rate=self.exchange_rate_spin.value(),
                goods_value=self.goods_value_spin.value(),
                shipping_cost=self.shipping_cost_spin.value(),
                insurance_cost=self.insurance_cost_spin.value(),
                customs_fees=self.customs_fees_spin.value(),
                other_fees=self.other_fees_spin.value(),
                total_costs=self.total_costs_spin.value(),
                payment_status=self.payment_status_combo.currentText(),
                paid_amount=self.paid_amount_spin.value(),
                remaining_amount=self.remaining_amount_spin.value(),
                payment_date=self.payment_date_edit.date().toPython(),
                payment_method=self.payment_method_combo.currentText(),
                financial_notes=self.financial_notes_edit.toPlainText(),
                # بيانات الشحن
                shipping_company=self.shipping_company_edit.text(),
                shipping_type=self.shipping_type_combo.currentText(),
                shipping_method=self.shipping_method_combo.currentText(),
                incoterms=self.incoterms_combo.currentText(),
                port_of_loading=self.port_of_loading_edit.text(),
                port_of_discharge=self.port_of_discharge_edit.text(),
                final_destination=self.final_destination_edit.text(),
                vessel_name=self.vessel_name_edit.text(),
                voyage_number=self.voyage_number_edit.text(),
                bill_of_lading_number=self.bill_of_lading_number_edit.text(),
                estimated_departure_date=self.estimated_departure_date.date().toPython(),
                actual_departure_date=self.actual_departure_date.date().toPython(),
                estimated_arrival_date=self.estimated_arrival_date.date().toPython(),
                actual_arrival_date=self.actual_arrival_date.date().toPython(),
                total_weight=self.total_weight_spin.value(),
                total_volume=self.total_volume_spin.value(),
                packages_count=self.packages_count_spin.value(),
                packaging_type=self.packaging_type_combo.currentText(),
                shipping_notes=self.shipping_notes_edit.toPlainText(),
                # روابط المستندات
                initial_documents_url=self.initial_docs_edit.text(),
                dn_documents_url=self.dn_docs_edit.text(),
                customs_documents_url=self.customs_docs_edit.text(),
                bill_of_lading_url=self.bill_lading_edit.text(),
                items_images_url=self.items_images_edit.text(),
                other_documents_url=self.other_docs_edit.text(),
                # مرفقات المستندات (فارغة في البداية)
                initial_documents_files="[]",
                dn_documents_files="[]",
                customs_documents_files="[]",
                bill_of_lading_files="[]",
                items_images_files="[]",
                other_documents_files="[]"
            )

            session.add(new_shipment)
            session.flush()  # للحصول على ID الشحنة

            # إضافة أصناف الشحنة
            for item_data in shipment_items:
                shipment_item = ShipmentItem(
                    shipment_id=new_shipment.id,
                    item_id=item_data['item_id'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    total_price=item_data['total_price'],
                    weight=item_data['weight'],
                    notes=item_data['notes']
                )
                session.add(shipment_item)

            # حفظ حاويات الشحنة
            containers = self.get_shipment_containers()
            for container_data in containers:
                container = Container(
                    shipment_id=new_shipment.id,
                    container_number=container_data['container_number'],
                    container_type=container_data['container_type'],
                    size=container_data['size'],
                    empty_weight=container_data['empty_weight'],
                    loaded_weight=container_data['loaded_weight'],
                    max_weight=container_data['max_weight'],
                    status=container_data['status'],
                    seal_number=container_data['seal_number'],
                    temperature=container_data['temperature'],
                    notes=container_data['notes']
                )
                session.add(container)

            # حفظ مستندات الشحنة
            documents = self.get_shipment_documents()
            for document_data in documents:
                document = ShipmentDocument(
                    shipment_id=new_shipment.id,
                    document_name=document_data['name'],
                    document_type=document_data['type'],
                    file_path=document_data['path'],
                    document_category=document_data['category'],
                    notes=document_data['notes']
                )
                session.add(document)

            session.commit()

            self.current_shipment_id = new_shipment.id
            self.edit_button.setEnabled(True)  # تفعيل زر التعديل بعد الحفظ
            self.shipment_saved.emit(new_shipment.id)

            QMessageBox.information(self, "نجح", "تم حفظ الشحنة وأصنافها بنجاح")
            # لا نغلق النافذة تلقائياً للسماح بالتعديل
            # self.accept()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الشحنة: {str(e)}")
        finally:
            session.close()
            
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.shipment_number_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يجب إدخال رقم الشحنة")
            return False
            
        if not self.supplier_edit.property("supplier_id"):
            QMessageBox.warning(self, "تحذير", "يجب اختيار المورد")
            return False
            
        return True

    def add_item(self):
        """إضافة صنف للشحنة"""
        dialog = ItemSearchDialog(self)
        if dialog.exec() == QDialog.Accepted:
            item = dialog.get_selected_item()
            if item:
                self.add_item_to_table(item)

    def add_item_to_table(self, item):
        """إضافة صنف إلى الجدول"""
        # التحقق من عدم وجود الصنف مسبقاً
        for row in range(self.items_table.rowCount()):
            existing_code = self.items_table.item(row, 0).text()
            if existing_code == item.code:
                QMessageBox.warning(self, "تحذير", "هذا الصنف موجود بالفعل في الشحنة")
                return

        # إضافة صف جديد
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # كود الصنف
        code_item = QTableWidgetItem(item.code)
        code_item.setData(Qt.UserRole, item.id)
        code_item.setFlags(code_item.flags() & ~Qt.ItemIsEditable)
        self.items_table.setItem(row, 0, code_item)

        # اسم الصنف
        name_item = QTableWidgetItem(item.name)
        name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
        self.items_table.setItem(row, 1, name_item)

        # الكمية (قابلة للتعديل)
        quantity_item = QTableWidgetItem("1.00")
        self.items_table.setItem(row, 2, quantity_item)

        # سعر الوحدة (قابل للتعديل)
        price_item = QTableWidgetItem(f"{item.cost_price:.2f}" if item.cost_price else "0.00")
        self.items_table.setItem(row, 3, price_item)

        # السعر الإجمالي (محسوب تلقائياً)
        total_price = float(quantity_item.text()) * float(price_item.text())
        total_item = QTableWidgetItem(f"{total_price:.2f}")
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
        total_item.setBackground(Qt.lightGray)
        self.items_table.setItem(row, 4, total_item)

        # الوزن (قابل للتعديل)
        weight_item = QTableWidgetItem(f"{item.weight:.2f}" if item.weight else "0.00")
        self.items_table.setItem(row, 5, weight_item)

        # ملاحظات (قابلة للتعديل)
        notes_item = QTableWidgetItem("")
        self.items_table.setItem(row, 6, notes_item)

        self.update_items_totals()

    def remove_item(self):
        """حذف صنف من الشحنة"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا الصنف؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.items_table.removeRow(current_row)
                self.update_items_totals()
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار صنف أولاً")

    def edit_item(self):
        """تعديل صنف في الشحنة"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            # يمكن تعديل الكمية وسعر الوحدة والوزن والملاحظات مباشرة في الجدول
            QMessageBox.information(self, "معلومات", "يمكنك تعديل الكمية وسعر الوحدة والوزن والملاحظات مباشرة في الجدول")
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار صنف أولاً")

    def on_item_selection_changed(self):
        """عند تغيير تحديد الصنف"""
        has_selection = self.items_table.currentRow() >= 0
        self.remove_item_button.setEnabled(has_selection)
        self.edit_item_button.setEnabled(has_selection)

    def on_item_data_changed(self, item):
        """عند تغيير بيانات صنف"""
        row = item.row()
        col = item.column()

        # إعادة حساب السعر الإجمالي عند تغيير الكمية أو سعر الوحدة
        if col in [2, 3]:  # الكمية أو سعر الوحدة
            try:
                quantity = float(self.items_table.item(row, 2).text())
                unit_price = float(self.items_table.item(row, 3).text())
                total_price = quantity * unit_price

                total_item = QTableWidgetItem(f"{total_price:.2f}")
                total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
                total_item.setBackground(Qt.lightGray)
                self.items_table.setItem(row, 4, total_item)

                self.update_items_totals()

            except ValueError:
                QMessageBox.warning(self, "خطأ", "يجب إدخال قيم رقمية صحيحة")

    def update_items_totals(self):
        """تحديث إجماليات الأصناف"""
        total_items = self.items_table.rowCount()
        total_amount = 0.0

        for row in range(total_items):
            try:
                amount = float(self.items_table.item(row, 4).text())
                total_amount += amount
            except (ValueError, AttributeError):
                continue

        self.total_items_label.setText(f"إجمالي الأصناف: {total_items}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:.2f}")

        # تحديث الحسابات المالية
        self.calculate_totals()

    def get_shipment_items(self):
        """الحصول على أصناف الشحنة"""
        items = []
        for row in range(self.items_table.rowCount()):
            try:
                item_data = {
                    'item_id': self.items_table.item(row, 0).data(Qt.UserRole),
                    'quantity': float(self.items_table.item(row, 2).text()),
                    'unit_price': float(self.items_table.item(row, 3).text()),
                    'total_price': float(self.items_table.item(row, 4).text()),
                    'weight': float(self.items_table.item(row, 5).text()) if self.items_table.item(row, 5).text() else 0.0,
                    'notes': self.items_table.item(row, 6).text() if self.items_table.item(row, 6) else ""
                }
                items.append(item_data)
            except (ValueError, AttributeError) as e:
                QMessageBox.warning(self, "خطأ", f"خطأ في بيانات الصف {row + 1}: {str(e)}")
                return None

        return items

    def load_currencies(self):
        """تحميل العملات"""
        session = self.db_manager.get_session()
        try:
            currencies = session.query(Currency).filter(Currency.is_active == True).all()

            self.currency_combo.clear()
            for currency in currencies:
                self.currency_combo.addItem(f"{currency.name} ({currency.code})", currency.id)

            # تحديد الريال السعودي كافتراضي إذا وجد
            for i in range(self.currency_combo.count()):
                if "SAR" in self.currency_combo.itemText(i):
                    self.currency_combo.setCurrentIndex(i)
                    break

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"خطأ في تحميل العملات: {str(e)}")
        finally:
            session.close()

    def calculate_totals(self):
        """حساب الإجماليات المالية"""
        try:
            # قيمة البضاعة من جدول الأصناف
            goods_value = 0.0
            for row in range(self.items_table.rowCount()):
                try:
                    amount = float(self.items_table.item(row, 4).text())
                    goods_value += amount
                except (ValueError, AttributeError):
                    continue

            self.goods_value_spin.setValue(goods_value)

            # حساب إجمالي التكاليف
            shipping_cost = self.shipping_cost_spin.value()
            insurance_cost = self.insurance_cost_spin.value()
            customs_fees = self.customs_fees_spin.value()
            other_fees = self.other_fees_spin.value()

            total_costs = goods_value + shipping_cost + insurance_cost + customs_fees + other_fees
            self.total_costs_spin.setValue(total_costs)

            # حساب المبلغ بالعملة المحلية
            exchange_rate = self.exchange_rate_spin.value()
            total_local = total_costs * exchange_rate
            self.total_local_currency_spin.setValue(total_local)

            # حساب المبلغ المتبقي
            self.calculate_remaining_amount()

        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {str(e)}")

    def calculate_remaining_amount(self):
        """حساب المبلغ المتبقي"""
        try:
            total_amount = self.total_local_currency_spin.value()
            paid_amount = self.paid_amount_spin.value()
            remaining = max(0, total_amount - paid_amount)
            self.remaining_amount_spin.setValue(remaining)

            # تحديث حالة الدفع تلقائياً
            if remaining == 0 and total_amount > 0:
                self.payment_status_combo.setCurrentText("تم الدفع بالكامل")
            elif paid_amount > 0 and remaining > 0:
                self.payment_status_combo.setCurrentText("دفع جزئي")
            elif paid_amount == 0:
                self.payment_status_combo.setCurrentText("لم يتم الدفع")

        except Exception as e:
            print(f"خطأ في حساب المبلغ المتبقي: {str(e)}")

    def add_container(self):
        """إضافة حاوية جديدة"""
        dialog = ContainerDialog(self)
        if dialog.exec() == QDialog.Accepted:
            container_data = dialog.get_container_data()
            if container_data:
                self.add_container_to_table(container_data)

    def add_container_to_table(self, container_data):
        """إضافة حاوية إلى الجدول"""
        # التحقق من عدم وجود الحاوية مسبقاً
        for row in range(self.containers_table.rowCount()):
            existing_number = self.containers_table.item(row, 0).text()
            if existing_number == container_data['container_number']:
                QMessageBox.warning(self, "تحذير", "هذه الحاوية موجودة بالفعل في الشحنة")
                return

        # إضافة صف جديد
        row = self.containers_table.rowCount()
        self.containers_table.insertRow(row)

        # رقم الحاوية
        number_item = QTableWidgetItem(container_data['container_number'])
        self.containers_table.setItem(row, 0, number_item)

        # نوع الحاوية
        type_item = QTableWidgetItem(container_data['container_type'])
        self.containers_table.setItem(row, 1, type_item)

        # الحجم
        size_item = QTableWidgetItem(container_data['size'])
        self.containers_table.setItem(row, 2, size_item)

        # الوزن الفارغ
        empty_weight_item = QTableWidgetItem(f"{container_data['empty_weight']:.2f}")
        self.containers_table.setItem(row, 3, empty_weight_item)

        # الوزن المحمل
        loaded_weight_item = QTableWidgetItem(f"{container_data['loaded_weight']:.2f}")
        self.containers_table.setItem(row, 4, loaded_weight_item)

        # الحالة
        status_item = QTableWidgetItem(container_data['status'])
        self.containers_table.setItem(row, 5, status_item)

        # رقم الختم
        seal_item = QTableWidgetItem(container_data['seal_number'])
        self.containers_table.setItem(row, 6, seal_item)

        # ملاحظات
        notes_item = QTableWidgetItem(container_data['notes'])
        self.containers_table.setItem(row, 7, notes_item)

        # حفظ البيانات الكاملة في الصف الأول
        number_item.setData(Qt.UserRole, container_data)

        self.update_containers_totals()

    def remove_container(self):
        """حذف حاوية من الشحنة"""
        current_row = self.containers_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه الحاوية؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.containers_table.removeRow(current_row)
                self.update_containers_totals()
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حاوية أولاً")

    def edit_container(self):
        """تعديل حاوية في الشحنة"""
        current_row = self.containers_table.currentRow()
        if current_row >= 0:
            # الحصول على البيانات الحالية
            current_data = self.containers_table.item(current_row, 0).data(Qt.UserRole)

            dialog = ContainerDialog(self, current_data)
            if dialog.exec() == QDialog.Accepted:
                container_data = dialog.get_container_data()
                if container_data:
                    # تحديث البيانات في الجدول
                    self.containers_table.item(current_row, 0).setText(container_data['container_number'])
                    self.containers_table.item(current_row, 1).setText(container_data['container_type'])
                    self.containers_table.item(current_row, 2).setText(container_data['size'])
                    self.containers_table.item(current_row, 3).setText(f"{container_data['empty_weight']:.2f}")
                    self.containers_table.item(current_row, 4).setText(f"{container_data['loaded_weight']:.2f}")
                    self.containers_table.item(current_row, 5).setText(container_data['status'])
                    self.containers_table.item(current_row, 6).setText(container_data['seal_number'])
                    self.containers_table.item(current_row, 7).setText(container_data['notes'])

                    # تحديث البيانات المحفوظة
                    self.containers_table.item(current_row, 0).setData(Qt.UserRole, container_data)

                    self.update_containers_totals()
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار حاوية أولاً")

    def on_container_selection_changed(self):
        """عند تغيير تحديد الحاوية"""
        has_selection = self.containers_table.currentRow() >= 0
        self.remove_container_button.setEnabled(has_selection)
        self.edit_container_button.setEnabled(has_selection)

    def update_containers_totals(self):
        """تحديث إجماليات الحاويات"""
        total_containers = self.containers_table.rowCount()
        total_weight = 0.0

        for row in range(total_containers):
            try:
                weight = float(self.containers_table.item(row, 4).text())
                total_weight += weight
            except (ValueError, AttributeError):
                continue

        self.total_containers_label.setText(f"إجمالي الحاويات: {total_containers}")
        self.total_container_weight_label.setText(f"إجمالي الوزن: {total_weight:.2f} كجم")

    def get_shipment_containers(self):
        """الحصول على حاويات الشحنة"""
        containers = []
        for row in range(self.containers_table.rowCount()):
            try:
                container_data = self.containers_table.item(row, 0).data(Qt.UserRole)
                if container_data:
                    containers.append(container_data)
            except AttributeError:
                continue

        return containers

    def add_document(self):
        """إضافة مستند جديد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار مستند", "",
            "جميع الملفات (*.*);;ملفات PDF (*.pdf);;ملفات Word (*.doc *.docx);;ملفات Excel (*.xls *.xlsx);;صور (*.jpg *.jpeg *.png *.gif)"
        )

        if file_path:
            file_name = os.path.basename(file_path)
            file_ext = os.path.splitext(file_name)[1].lower()

            # تحديد نوع المستند حسب الامتداد
            if file_ext in ['.pdf']:
                doc_type = "PDF"
            elif file_ext in ['.doc', '.docx']:
                doc_type = "Word"
            elif file_ext in ['.xls', '.xlsx']:
                doc_type = "Excel"
            elif file_ext in ['.jpg', '.jpeg', '.png', '.gif']:
                doc_type = "صورة"
            else:
                doc_type = "ملف"

            self.add_document_to_table(file_name, doc_type, file_path, "ملف")

    def add_specific_link(self, link_type, target_edit):
        """إضافة رابط محدد لنوع مستند معين"""
        existing_url = target_edit.text().strip()
        existing_description = ""

        dialog = AddLinkDialog(self, link_type, existing_url, existing_description)
        if dialog.exec() == QDialog.Accepted:
            link_data = dialog.get_link_data()
            url = link_data['url']
            description = link_data['description']

            # تحديث الحقل
            target_edit.setText(url)

            # تطبيق تنسيق الرابط التشعبي
            target_edit.setStyleSheet("""
                QLineEdit {
                    color: #0066cc;
                    text-decoration: underline;
                    background-color: #f0f8ff;
                    border: 2px solid #0066cc;
                    border-radius: 3px;
                    padding: 5px;
                }
                QLineEdit:hover {
                    background-color: #e6f3ff;
                }
            """)

            # إضافة إمكانية النقر لفتح الرابط
            target_edit.mousePressEvent = lambda event: self.open_url(url) if event.button() == Qt.LeftButton else None

            # إضافة tooltip مع الوصف
            if description:
                target_edit.setToolTip(f"{link_type}\n{description}\nانقر لفتح الرابط")
            else:
                target_edit.setToolTip(f"{link_type}\nانقر لفتح الرابط")

    def add_link(self):
        """إضافة رابط تشعبي"""
        from PySide6.QtWidgets import QInputDialog

        # طلب الرابط
        url, ok = QInputDialog.getText(self, "إضافة رابط", "أدخل الرابط:")
        if not ok or not url.strip():
            return

        # طلب اسم الرابط
        name, ok = QInputDialog.getText(self, "اسم الرابط", "أدخل اسم الرابط:")
        if not ok or not name.strip():
            name = url

        self.add_document_to_table(name, "رابط", url, "رابط")

    def open_url(self, url):
        """فتح الرابط في المتصفح"""
        try:
            QDesktopServices.openUrl(QUrl(url))
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح الرابط: {str(e)}")

    def add_document_to_table(self, name, doc_type, path, category):
        """إضافة مستند إلى الجدول"""
        row = self.documents_table.rowCount()
        self.documents_table.insertRow(row)

        # اسم المستند
        name_item = QTableWidgetItem(name)
        self.documents_table.setItem(row, 0, name_item)

        # النوع
        type_item = QTableWidgetItem(doc_type)
        self.documents_table.setItem(row, 1, type_item)

        # المسار/الرابط
        path_item = QTableWidgetItem(path)
        self.documents_table.setItem(row, 2, path_item)

        # تاريخ الإضافة
        date_item = QTableWidgetItem(QDate.currentDate().toString("yyyy-MM-dd"))
        self.documents_table.setItem(row, 3, date_item)

        # ملاحظات
        notes_item = QTableWidgetItem("")
        self.documents_table.setItem(row, 4, notes_item)

        # حفظ البيانات الإضافية
        document_data = {
            'name': name,
            'type': doc_type,
            'path': path,
            'category': category,
            'date_added': QDate.currentDate().toString("yyyy-MM-dd")
        }
        name_item.setData(Qt.UserRole, document_data)

        self.update_documents_totals()

    def remove_document(self):
        """حذف مستند"""
        current_row = self.documents_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا المستند؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.documents_table.removeRow(current_row)
                self.update_documents_totals()
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار مستند أولاً")

    def open_document(self):
        """فتح مستند أو رابط"""
        current_row = self.documents_table.currentRow()
        if current_row >= 0:
            path = self.documents_table.item(current_row, 2).text()
            doc_type = self.documents_table.item(current_row, 1).text()

            try:
                if doc_type == "رابط":
                    # فتح الرابط في المتصفح
                    QDesktopServices.openUrl(QUrl(path))
                else:
                    # فتح الملف
                    if os.path.exists(path):
                        QDesktopServices.openUrl(QUrl.fromLocalFile(path))
                    else:
                        QMessageBox.warning(self, "خطأ", f"الملف غير موجود:\n{path}")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في فتح المستند:\n{str(e)}")
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار مستند أولاً")

    def on_document_selection_changed(self):
        """عند تغيير تحديد المستند"""
        has_selection = self.documents_table.currentRow() >= 0
        self.remove_document_button.setEnabled(has_selection)
        self.open_document_button.setEnabled(has_selection)

    def update_documents_totals(self):
        """تحديث إجمالي المستندات"""
        total_documents = self.documents_table.rowCount()
        self.total_documents_label.setText(f"إجمالي المستندات: {total_documents}")

    def get_shipment_documents(self):
        """الحصول على مستندات الشحنة"""
        documents = []
        for row in range(self.documents_table.rowCount()):
            try:
                document_data = self.documents_table.item(row, 0).data(Qt.UserRole)
                if document_data:
                    # إضافة الملاحظات من الجدول
                    notes_item = self.documents_table.item(row, 4)
                    document_data['notes'] = notes_item.text() if notes_item else ""
                    documents.append(document_data)
            except AttributeError:
                continue

        return documents

    def manage_attachments(self, document_type, field_name):
        """إدارة مرفقات نوع مستند محدد"""
        try:
            # الحصول على الملفات الموجودة
            existing_files = []
            if hasattr(self, 'current_shipment_id') and self.current_shipment_id:
                # تحميل من قاعدة البيانات
                session = self.db_manager.get_session()
                shipment = session.query(Shipment).filter(Shipment.id == self.current_shipment_id).first()
                if shipment:
                    files_json = getattr(shipment, field_name)
                    if files_json:
                        import json
                        existing_files = json.loads(files_json)
                session.close()

            # فتح نافذة إدارة المرفقات
            dialog = AttachmentsManagerDialog(self, document_type, existing_files)
            if dialog.exec() == QDialog.Accepted:
                # الحصول على قائمة الملفات المحدثة
                updated_files = dialog.get_files_list()

                # حفظ في قاعدة البيانات إذا كانت الشحنة محفوظة
                if hasattr(self, 'current_shipment_id') and self.current_shipment_id:
                    self.save_attachments_to_database(field_name, updated_files)

                # تحديث عداد المرفقات في الزر
                self.update_attachment_button_count(document_type, field_name, len(updated_files))

                QMessageBox.information(self, "نجح", f"تم تحديث مرفقات {document_type} بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إدارة المرفقات: {str(e)}")

    def save_attachments_to_database(self, field_name, files_list):
        """حفظ المرفقات في قاعدة البيانات"""
        try:
            import json
            session = self.db_manager.get_session()

            shipment = session.query(Shipment).filter(Shipment.id == self.current_shipment_id).first()
            if shipment:
                # تحويل قائمة الملفات إلى JSON
                files_json = json.dumps(files_list, ensure_ascii=False)
                setattr(shipment, field_name, files_json)

                session.commit()

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المرفقات: {str(e)}")

    def update_attachment_button_count(self, document_type, field_name, count):
        """تحديث عداد المرفقات في الزر"""
        button_mapping = {
            "المستندات الأولية": self.initial_docs_attach_button,
            "المستندات (DN)": self.dn_docs_attach_button,
            "المستندات المرسلة للجمارك": self.customs_docs_attach_button,
            "بوليصة الشحن": self.bill_lading_attach_button,
            "صور الأصناف": self.items_images_attach_button,
            "مستندات أخرى": self.other_docs_attach_button
        }

        button = button_mapping.get(document_type)
        if button:
            if count > 0:
                button.setText(f"إضافة مرفق ({count})")
                button.setStyleSheet("QPushButton { background-color: #e67e22; color: white; padding: 5px 10px; }")
            else:
                button.setText("إضافة مرفق")
                button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; }")

    def add_attachment(self):
        """إضافة مرفق للمستندات الإضافية"""
        try:
            dialog = AttachmentsManagerDialog(self, "مستندات إضافية", [])
            if dialog.exec() == QDialog.Accepted:
                files_list = dialog.get_files_list()

                # إضافة الملفات إلى الجدول
                for file_path in files_list:
                    file_name = os.path.basename(file_path)
                    self.add_document_to_table(file_name, "مرفق", file_path, "إضافية")

                QMessageBox.information(self, "نجح", f"تم إضافة {len(files_list)} مرفق بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المرفقات: {str(e)}")

    def load_attachment_counts(self, shipment_id):
        """تحميل عدادات المرفقات للشحنة"""
        try:
            import json
            session = self.db_manager.get_session()

            shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
            if shipment:
                # تحديث عدادات المرفقات
                attachment_fields = {
                    "المستندات الأولية": "initial_documents_files",
                    "المستندات (DN)": "dn_documents_files",
                    "المستندات المرسلة للجمارك": "customs_documents_files",
                    "بوليصة الشحن": "bill_of_lading_files",
                    "صور الأصناف": "items_images_files",
                    "مستندات أخرى": "other_documents_files"
                }

                for doc_type, field_name in attachment_fields.items():
                    files_json = getattr(shipment, field_name)
                    if files_json:
                        try:
                            files_list = json.loads(files_json)
                            count = len(files_list) if files_list else 0
                            self.update_attachment_button_count(doc_type, field_name, count)
                        except json.JSONDecodeError:
                            self.update_attachment_button_count(doc_type, field_name, 0)
                    else:
                        self.update_attachment_button_count(doc_type, field_name, 0)

            session.close()

        except Exception as e:
            print(f"خطأ في تحميل عدادات المرفقات: {str(e)}")

    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        reply = QMessageBox.question(
            self,
            "شحنة جديدة",
            "هل تريد إنشاء شحنة جديدة؟\nسيتم مسح البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.clear_form()
            self.current_shipment_id = None
            self.edit_button.setEnabled(False)
            self.generate_shipment_number()
            QMessageBox.information(self, "شحنة جديدة", "تم إنشاء نموذج شحنة جديد")

    def edit_shipment(self):
        """تعديل الشحنة الحالية"""
        if self.current_shipment_id is None:
            QMessageBox.warning(self, "تحذير", "لا توجد شحنة محفوظة للتعديل")
            return

        reply = QMessageBox.question(
            self,
            "تعديل الشحنة",
            "هل تريد تعديل الشحنة الحالية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # تمكين جميع الحقول للتعديل
            self.enable_form_editing(True)
            QMessageBox.information(self, "تعديل الشحنة", "يمكنك الآن تعديل بيانات الشحنة")

    def clear_form(self):
        """مسح جميع بيانات النموذج"""
        try:
            # مسح البيانات الأساسية
            self.shipment_number_edit.clear()
            self.supplier_code_edit.clear()
            self.supplier_name_edit.clear()
            self.shipment_date_edit.setDate(QDate.currentDate())
            self.expected_arrival_date_edit.setDate(QDate.currentDate().addDays(30))
            self.shipment_status_combo.setCurrentIndex(0)
            self.clearance_status_combo.setCurrentIndex(0)
            self.notes_edit.clear()

            # مسح جدول الأصناف
            self.items_table.setRowCount(0)

            # مسح البيانات المالية
            self.shipping_cost_spin.setValue(0)
            self.insurance_cost_spin.setValue(0)
            self.customs_fees_spin.setValue(0)
            self.other_fees_spin.setValue(0)
            self.exchange_rate_spin.setValue(1)
            self.paid_amount_spin.setValue(0)
            self.payment_status_combo.setCurrentIndex(0)

            # مسح بيانات الشحن
            self.shipping_company_edit.clear()
            self.vessel_name_edit.clear()
            self.departure_port_edit.clear()
            self.arrival_port_edit.clear()
            self.tracking_number_edit.clear()
            self.departure_date_edit.setDate(QDate.currentDate())
            self.arrival_date_edit.setDate(QDate.currentDate().addDays(30))

            # مسح جدول الحاويات
            self.containers_table.setRowCount(0)

            # مسح روابط المستندات
            for field_name in ['initial_docs_url', 'dn_docs_url', 'customs_docs_url',
                             'shipping_bill_url', 'item_images_url', 'other_docs_url']:
                if hasattr(self, f'{field_name}_edit'):
                    getattr(self, f'{field_name}_edit').clear()

            # مسح جدول المستندات الإضافية
            self.documents_table.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في مسح النموذج: {str(e)}")

    def enable_form_editing(self, enabled=True):
        """تمكين أو تعطيل تحرير النموذج"""
        try:
            # تمكين/تعطيل الحقول الأساسية
            self.supplier_code_edit.setEnabled(enabled)
            self.shipment_date_edit.setEnabled(enabled)
            self.expected_arrival_date_edit.setEnabled(enabled)
            self.shipment_status_combo.setEnabled(enabled)
            self.clearance_status_combo.setEnabled(enabled)
            self.notes_edit.setEnabled(enabled)

            # تمكين/تعطيل أزرار الأصناف
            self.add_item_button.setEnabled(enabled)
            self.remove_item_button.setEnabled(enabled)
            self.edit_item_button.setEnabled(enabled)

            # تمكين/تعطيل الحقول المالية
            self.shipping_cost_spin.setEnabled(enabled)
            self.insurance_cost_spin.setEnabled(enabled)
            self.customs_fees_spin.setEnabled(enabled)
            self.other_fees_spin.setEnabled(enabled)
            self.exchange_rate_spin.setEnabled(enabled)
            self.paid_amount_spin.setEnabled(enabled)
            self.payment_status_combo.setEnabled(enabled)

            # تمكين/تعطيل حقول الشحن
            self.shipping_company_edit.setEnabled(enabled)
            self.vessel_name_edit.setEnabled(enabled)
            self.departure_port_edit.setEnabled(enabled)
            self.arrival_port_edit.setEnabled(enabled)
            self.tracking_number_edit.setEnabled(enabled)
            self.departure_date_edit.setEnabled(enabled)
            self.arrival_date_edit.setEnabled(enabled)

            # تمكين/تعطيل أزرار الحاويات
            self.add_container_button.setEnabled(enabled)
            self.remove_container_button.setEnabled(enabled)
            self.edit_container_button.setEnabled(enabled)

            # تمكين/تعطيل أزرار المستندات
            self.add_document_button.setEnabled(enabled)
            self.add_link_button.setEnabled(enabled)
            self.remove_document_button.setEnabled(enabled)

        except Exception as e:
            print(f"خطأ في تمكين/تعطيل النموذج: {str(e)}")
