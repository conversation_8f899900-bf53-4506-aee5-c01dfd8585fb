#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة
هذه النافذة تحل محل النافذة الأصلية المعقدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                               QTabWidget, QWidget, QGroupBox, QFormLayout,
                               QLineEdit, QComboBox, QTextEdit, QDateEdit,
                               QSpinBox, QTableWidget, QMessageBox, QLabel,
                               QFrame, QToolBar, QDialogButtonBox, QApplication,
                               QHeader<PERSON>iew, QA<PERSON>tract<PERSON>tem<PERSON>iew, QGridLayout,
                               QDoubleSpinBox, QTableWidgetItem)
from PySide6.QtCore import Signal, QDate, Qt, QSize
from PySide6.QtGui import QFont, QAction

class NewShipmentWindow(QDialog):
    """نافذة شحنة جديدة نهائية مع أزرار التحكم العاملة"""
    
    shipment_saved = Signal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_shipment_id = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🚢 شحنة جديدة - مع أزرار التحكم")
        self.setModal(True)
        self.resize(1000, 750)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        self.create_title_header(main_layout)
        
        # شريط أزرار التحكم
        self.create_control_toolbar(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        # أزرار الحفظ والإلغاء
        self.create_dialog_buttons(main_layout)
        
    def create_title_header(self, main_layout):
        """إنشاء عنوان النافذة"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 12px;
                padding: 15px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🚢 نظام إدارة الشحنات - شحنة جديدة")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 10px;
            }
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        main_layout.addWidget(title_frame)
        
    def create_control_toolbar(self, main_layout):
        """إنشاء شريط أزرار التحكم"""
        # إطار الأزرار
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel | QFrame.Raised)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 3px solid #3498db;
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setSpacing(20)
        toolbar_layout.setContentsMargins(25, 20, 25, 20)

        # زر إضافة
        self.new_button = QPushButton("🆕 إضافة")
        self.new_button.setFixedSize(160, 60)
        self.new_button.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))

        # زر حفظ
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setFixedSize(160, 60)
        self.save_button.setStyleSheet(self.get_button_style("#27ae60", "#229954"))

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setFixedSize(160, 60)
        self.edit_button.setStyleSheet(self.get_button_style("#f39c12", "#e67e22"))
        self.edit_button.setEnabled(False)

        # زر خروج
        self.exit_button = QPushButton("🚪 خروج")
        self.exit_button.setFixedSize(160, 60)
        self.exit_button.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))

        # ترتيب الأزرار
        toolbar_layout.addWidget(self.new_button)
        toolbar_layout.addWidget(self.save_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.exit_button)
        
        main_layout.addWidget(toolbar_frame)
        
    def get_button_style(self, color1, color2):
        """الحصول على نمط الزر"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                font-weight: bold;
                font-size: 16px;
                border: 3px solid {color2};
                border-radius: 15px;
            }}
            QPushButton:hover {{
                background: {color2};
                border: 3px solid {color1};
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background: {color1};
            }}
            QPushButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
                border: 3px solid #95a5a6;
            }}
        """
        
    def create_tabs(self, main_layout):
        """إنشاء التبويبات"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #bdc3c7;
                border-radius: 10px;
                background-color: white;
                padding: 10px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 12px 25px;
                margin: 3px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # إنشاء التبويبات
        self.create_basic_tab()
        self.create_items_tab()
        self.create_financial_tab()
        self.create_shipping_tab()
        self.create_containers_tab()
        self.create_documents_tab()
        
        main_layout.addWidget(self.tab_widget)
        
    def create_basic_tab(self):
        """إنشاء التبويب الأساسي"""
        basic_tab = QWidget()
        self.tab_widget.addTab(basic_tab, "📋 البيانات الأساسية")
        
        layout = QVBoxLayout(basic_tab)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #2c3e50;
                background-color: white;
            }
        """)
        
        basic_layout = QGridLayout(basic_group)
        basic_layout.setSpacing(8)

        # الصف الأول: رقم الشحنة + المورد
        basic_layout.addWidget(QLabel("رقم الشحنة:"), 0, 0)
        self.shipment_number_edit = QLineEdit()
        self.shipment_number_edit.setReadOnly(True)
        self.shipment_number_edit.setText("SH-2024-001")
        self.shipment_number_edit.setStyleSheet(self.get_input_style(readonly=True))
        basic_layout.addWidget(self.shipment_number_edit, 0, 1)

        basic_layout.addWidget(QLabel("المورد:"), 0, 2)
        supplier_layout = QHBoxLayout()
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setReadOnly(True)
        self.supplier_edit.setStyleSheet(self.get_input_style())
        self.supplier_search_button = QPushButton("F9")
        self.supplier_search_button.setMaximumWidth(35)
        self.supplier_search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        supplier_layout.addWidget(self.supplier_edit)
        supplier_layout.addWidget(self.supplier_search_button)
        supplier_widget = QWidget()
        supplier_widget.setLayout(supplier_layout)
        basic_layout.addWidget(supplier_widget, 0, 3)

        # الصف الثاني: فاتورة المورد + حالة الشحنة
        basic_layout.addWidget(QLabel("فاتورة المورد:"), 1, 0)
        self.supplier_invoice_edit = QLineEdit()
        self.supplier_invoice_edit.setPlaceholderText("رقم فاتورة المورد...")
        self.supplier_invoice_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.supplier_invoice_edit, 1, 1)

        basic_layout.addWidget(QLabel("حالة الشحنة:"), 1, 2)
        self.shipment_status_combo = QComboBox()
        self.shipment_status_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق",
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        self.shipment_status_combo.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.shipment_status_combo, 1, 3)

        # الصف الثالث: حالة الإفراج + رقم التتبع
        basic_layout.addWidget(QLabel("حالة الإفراج:"), 2, 0)
        self.clearance_status_combo = QComboBox()
        self.clearance_status_combo.addItems(["بدون الافراج", "مع الافراج"])
        self.clearance_status_combo.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.clearance_status_combo, 2, 1)

        basic_layout.addWidget(QLabel("رقم التتبع:"), 2, 2)
        self.tracking_number_edit = QLineEdit()
        self.tracking_number_edit.setPlaceholderText("رقم التتبع...")
        self.tracking_number_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.tracking_number_edit, 2, 3)

        # الصف الرابع: بوليصة الشحن + ملاحظات
        basic_layout.addWidget(QLabel("بوليصة الشحن:"), 3, 0)
        self.bill_of_lading_edit = QLineEdit()
        self.bill_of_lading_edit.setPlaceholderText("رقم بوليصة الشحن...")
        self.bill_of_lading_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.bill_of_lading_edit, 3, 1)

        basic_layout.addWidget(QLabel("ملاحظات:"), 3, 2)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(50)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات...")
        self.notes_edit.setStyleSheet(self.get_input_style())
        basic_layout.addWidget(self.notes_edit, 3, 3)

        layout.addWidget(basic_group)
        layout.addStretch()
        
    def get_input_style(self, readonly=False):
        """الحصول على نمط الحقول"""
        bg_color = "#f8f9fa" if readonly else "white"
        return f"""
            QLineEdit, QComboBox, QTextEdit, QDateEdit {{
                background-color: {bg_color};
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                min-height: 20px;
            }}
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus, QDateEdit:focus {{
                border: 2px solid #3498db;
                background-color: #f8f9fa;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border: 2px solid #3498db;
                width: 10px;
                height: 10px;
            }}
        """
        
    def create_items_tab(self):
        """إنشاء تبويب الأصناف"""
        items_tab = QWidget()
        self.tab_widget.addTab(items_tab, "📦 الأصناف")

        layout = QVBoxLayout(items_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # أزرار إدارة الأصناف
        buttons_layout = QHBoxLayout()

        self.add_item_button = QPushButton("إضافة صنف (F9)")
        self.add_item_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.remove_item_button = QPushButton("حذف صنف")
        self.remove_item_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.remove_item_button.setEnabled(False)

        self.edit_item_button = QPushButton("تعديل صنف")
        self.edit_item_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        self.edit_item_button.setEnabled(False)

        buttons_layout.addWidget(self.add_item_button)
        buttons_layout.addWidget(self.remove_item_button)
        buttons_layout.addWidget(self.edit_item_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "كود الصنف", "اسم الصنف", "الكمية", "سعر الوحدة",
            "السعر الإجمالي", "الوزن", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # كود الصنف
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الصنف
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # سعر الوحدة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # السعر الإجمالي
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الوزن
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # ملاحظات

        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.items_table)

        # إجمالي الأصناف
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_items_label = QLabel("إجمالي الأصناف: 0")
        self.total_items_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        self.total_value_label = QLabel("إجمالي القيمة: 0.00")
        self.total_value_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_items_label)
        totals_layout.addWidget(self.total_value_label)

        layout.addLayout(totals_layout)
        
    def create_financial_tab(self):
        """إنشاء تبويب المالية"""
        financial_tab = QWidget()
        self.tab_widget.addTab(financial_tab, "💰 البيانات المالية")

        layout = QVBoxLayout(financial_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # مجموعة البيانات المالية
        financial_group = QGroupBox("البيانات المالية")
        financial_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #f39c12;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #f39c12;
                background-color: white;
            }
        """)
        financial_layout = QGridLayout(financial_group)
        financial_layout.setSpacing(8)

        # الصف الأول: العملة + سعر الصرف + قيمة البضاعة
        financial_layout.addWidget(QLabel("العملة:"), 0, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["USD", "EUR", "SAR", "AED", "KWD", "QAR", "BHD"])
        self.currency_combo.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.currency_combo, 0, 1)

        financial_layout.addWidget(QLabel("سعر الصرف:"), 0, 2)
        self.exchange_rate_spin = QDoubleSpinBox()
        self.exchange_rate_spin.setRange(0.01, 999999.99)
        self.exchange_rate_spin.setDecimals(4)
        self.exchange_rate_spin.setValue(1.0000)
        self.exchange_rate_spin.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.exchange_rate_spin, 0, 3)

        financial_layout.addWidget(QLabel("قيمة البضاعة:"), 0, 4)
        self.goods_value_spin = QDoubleSpinBox()
        self.goods_value_spin.setRange(0.00, 999999999.99)
        self.goods_value_spin.setDecimals(2)
        self.goods_value_spin.setReadOnly(True)
        self.goods_value_spin.setStyleSheet(self.get_input_style(readonly=True))
        financial_layout.addWidget(self.goods_value_spin, 0, 5)

        # الصف الثاني: تكلفة الشحن + التأمين + رسوم الجمارك
        financial_layout.addWidget(QLabel("تكلفة الشحن:"), 1, 0)
        self.shipping_cost_spin = QDoubleSpinBox()
        self.shipping_cost_spin.setRange(0.00, 999999999.99)
        self.shipping_cost_spin.setDecimals(2)
        self.shipping_cost_spin.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.shipping_cost_spin, 1, 1)

        financial_layout.addWidget(QLabel("تكلفة التأمين:"), 1, 2)
        self.insurance_cost_spin = QDoubleSpinBox()
        self.insurance_cost_spin.setRange(0.00, 999999999.99)
        self.insurance_cost_spin.setDecimals(2)
        self.insurance_cost_spin.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.insurance_cost_spin, 1, 3)

        financial_layout.addWidget(QLabel("رسوم الجمارك:"), 1, 4)
        self.customs_fees_spin = QDoubleSpinBox()
        self.customs_fees_spin.setRange(0.00, 999999999.99)
        self.customs_fees_spin.setDecimals(2)
        self.customs_fees_spin.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.customs_fees_spin, 1, 5)

        # الصف الثالث: رسوم أخرى + إجمالي التكاليف + إجمالي بالعملة المحلية
        financial_layout.addWidget(QLabel("رسوم أخرى:"), 2, 0)
        self.other_fees_spin = QDoubleSpinBox()
        self.other_fees_spin.setRange(0.00, 999999999.99)
        self.other_fees_spin.setDecimals(2)
        self.other_fees_spin.setStyleSheet(self.get_input_style())
        financial_layout.addWidget(self.other_fees_spin, 2, 1)

        financial_layout.addWidget(QLabel("إجمالي التكاليف:"), 2, 2)
        self.total_costs_spin = QDoubleSpinBox()
        self.total_costs_spin.setRange(0.00, 999999999.99)
        self.total_costs_spin.setDecimals(2)
        self.total_costs_spin.setReadOnly(True)
        self.total_costs_spin.setStyleSheet(self.get_input_style(readonly=True))
        financial_layout.addWidget(self.total_costs_spin, 2, 3)

        financial_layout.addWidget(QLabel("الإجمالي بالعملة المحلية:"), 2, 4)
        self.total_local_currency_spin = QDoubleSpinBox()
        self.total_local_currency_spin.setRange(0.00, 999999999.99)
        self.total_local_currency_spin.setDecimals(2)
        self.total_local_currency_spin.setReadOnly(True)
        self.total_local_currency_spin.setStyleSheet(self.get_input_style(readonly=True))
        financial_layout.addWidget(self.total_local_currency_spin, 2, 5)

        layout.addWidget(financial_group)

        # مجموعة الدفع
        payment_group = QGroupBox("معلومات الدفع")
        payment_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #27ae60;
                background-color: white;
            }
        """)
        payment_layout = QGridLayout(payment_group)
        payment_layout.setSpacing(8)

        # الصف الأول: حالة الدفع + المبلغ المدفوع + المبلغ المتبقي
        payment_layout.addWidget(QLabel("حالة الدفع:"), 0, 0)
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItems(["غير مدفوع", "مدفوع جزئياً", "مدفوع بالكامل", "مسترد"])
        self.payment_status_combo.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_status_combo, 0, 1)

        payment_layout.addWidget(QLabel("المبلغ المدفوع:"), 0, 2)
        self.paid_amount_spin = QDoubleSpinBox()
        self.paid_amount_spin.setRange(0.00, 999999999.99)
        self.paid_amount_spin.setDecimals(2)
        self.paid_amount_spin.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.paid_amount_spin, 0, 3)

        payment_layout.addWidget(QLabel("المبلغ المتبقي:"), 0, 4)
        self.remaining_amount_spin = QDoubleSpinBox()
        self.remaining_amount_spin.setRange(0.00, 999999999.99)
        self.remaining_amount_spin.setDecimals(2)
        self.remaining_amount_spin.setReadOnly(True)
        self.remaining_amount_spin.setStyleSheet(self.get_input_style(readonly=True))
        payment_layout.addWidget(self.remaining_amount_spin, 0, 5)

        # الصف الثاني: تاريخ الدفع + طريقة الدفع
        payment_layout.addWidget(QLabel("تاريخ الدفع:"), 1, 0)
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        self.payment_date_edit.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_date_edit, 1, 1)

        payment_layout.addWidget(QLabel("طريقة الدفع:"), 1, 2)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "شيك", "تحويل بنكي", "بطاقة ائتمان", "أخرى"])
        self.payment_method_combo.setStyleSheet(self.get_input_style())
        payment_layout.addWidget(self.payment_method_combo, 1, 3)

        layout.addWidget(payment_group)
        layout.addStretch()

    def create_shipping_tab(self):
        """إنشاء تبويب الشحن"""
        shipping_tab = QWidget()
        self.tab_widget.addTab(shipping_tab, "🚢 الشحن")

        layout = QVBoxLayout(shipping_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # مجموعة بيانات الشحن
        shipping_group = QGroupBox("بيانات الشحن")
        shipping_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #3498db;
                background-color: white;
            }
        """)
        shipping_layout = QGridLayout(shipping_group)
        shipping_layout.setSpacing(8)

        # الصف الأول: شركة الشحن + نوع الشحن + طريقة الشحن
        shipping_layout.addWidget(QLabel("شركة الشحن:"), 0, 0)
        self.shipping_company_edit = QLineEdit()
        self.shipping_company_edit.setPlaceholderText("اسم شركة الشحن...")
        self.shipping_company_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_company_edit, 0, 1)

        shipping_layout.addWidget(QLabel("نوع الشحن:"), 0, 2)
        self.shipping_type_combo = QComboBox()
        self.shipping_type_combo.addItems(["بحري", "جوي", "بري", "مختلط"])
        self.shipping_type_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_type_combo, 0, 3)

        shipping_layout.addWidget(QLabel("طريقة الشحن:"), 0, 4)
        self.shipping_method_combo = QComboBox()
        self.shipping_method_combo.addItems(["FCL", "LCL", "Air Freight", "Express"])
        self.shipping_method_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.shipping_method_combo, 0, 5)

        # الصف الثاني: شروط التسليم + ميناء الشحن + ميناء الوصول
        shipping_layout.addWidget(QLabel("شروط التسليم:"), 1, 0)
        self.incoterms_combo = QComboBox()
        self.incoterms_combo.addItems([
            "EXW", "FCA", "CPT", "CIP", "DAP", "DPU", "DDP",
            "FAS", "FOB", "CFR", "CIF"
        ])
        self.incoterms_combo.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.incoterms_combo, 1, 1)

        shipping_layout.addWidget(QLabel("ميناء الشحن:"), 1, 2)
        self.port_of_loading_edit = QLineEdit()
        self.port_of_loading_edit.setPlaceholderText("ميناء الشحن...")
        self.port_of_loading_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.port_of_loading_edit, 1, 3)

        shipping_layout.addWidget(QLabel("ميناء الوصول:"), 1, 4)
        self.port_of_discharge_edit = QLineEdit()
        self.port_of_discharge_edit.setPlaceholderText("ميناء الوصول...")
        self.port_of_discharge_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.port_of_discharge_edit, 1, 5)

        # الصف الثالث: الوجهة النهائية + اسم السفينة + رقم الرحلة
        shipping_layout.addWidget(QLabel("الوجهة النهائية:"), 2, 0)
        self.final_destination_edit = QLineEdit()
        self.final_destination_edit.setPlaceholderText("الوجهة النهائية...")
        self.final_destination_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.final_destination_edit, 2, 1)

        shipping_layout.addWidget(QLabel("اسم السفينة:"), 2, 2)
        self.vessel_name_edit = QLineEdit()
        self.vessel_name_edit.setPlaceholderText("اسم السفينة...")
        self.vessel_name_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.vessel_name_edit, 2, 3)

        shipping_layout.addWidget(QLabel("رقم الرحلة:"), 2, 4)
        self.voyage_number_edit = QLineEdit()
        self.voyage_number_edit.setPlaceholderText("رقم الرحلة...")
        self.voyage_number_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.voyage_number_edit, 2, 5)

        # الصف الرابع: رقم بوليصة الشحن
        shipping_layout.addWidget(QLabel("رقم بوليصة الشحن:"), 3, 0)
        self.bill_of_lading_number_edit = QLineEdit()
        self.bill_of_lading_number_edit.setPlaceholderText("رقم بوليصة الشحن...")
        self.bill_of_lading_number_edit.setStyleSheet(self.get_input_style())
        shipping_layout.addWidget(self.bill_of_lading_number_edit, 3, 1)

        layout.addWidget(shipping_group)

        # مجموعة التواريخ
        dates_group = QGroupBox("التواريخ")
        dates_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #27ae60;
                background-color: white;
            }
        """)
        dates_layout = QGridLayout(dates_group)
        dates_layout.setSpacing(8)

        # الصف الأول: تاريخ المغادرة المتوقع + الفعلي
        dates_layout.addWidget(QLabel("تاريخ المغادرة المتوقع:"), 0, 0)
        self.estimated_departure_date_edit = QDateEdit()
        self.estimated_departure_date_edit.setDate(QDate.currentDate())
        self.estimated_departure_date_edit.setCalendarPopup(True)
        self.estimated_departure_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.estimated_departure_date_edit, 0, 1)

        dates_layout.addWidget(QLabel("تاريخ المغادرة الفعلي:"), 0, 2)
        self.actual_departure_date_edit = QDateEdit()
        self.actual_departure_date_edit.setDate(QDate.currentDate())
        self.actual_departure_date_edit.setCalendarPopup(True)
        self.actual_departure_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.actual_departure_date_edit, 0, 3)

        # الصف الثاني: تاريخ الوصول المتوقع + الفعلي
        dates_layout.addWidget(QLabel("تاريخ الوصول المتوقع:"), 1, 0)
        self.estimated_arrival_date_edit = QDateEdit()
        self.estimated_arrival_date_edit.setDate(QDate.currentDate().addDays(30))
        self.estimated_arrival_date_edit.setCalendarPopup(True)
        self.estimated_arrival_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.estimated_arrival_date_edit, 1, 1)

        dates_layout.addWidget(QLabel("تاريخ الوصول الفعلي:"), 1, 2)
        self.actual_arrival_date_edit = QDateEdit()
        self.actual_arrival_date_edit.setDate(QDate.currentDate())
        self.actual_arrival_date_edit.setCalendarPopup(True)
        self.actual_arrival_date_edit.setStyleSheet(self.get_input_style())
        dates_layout.addWidget(self.actual_arrival_date_edit, 1, 3)

        layout.addWidget(dates_group)
        layout.addStretch()

    def create_containers_tab(self):
        """إنشاء تبويب الحاويات"""
        containers_tab = QWidget()
        self.tab_widget.addTab(containers_tab, "📦 الحاويات")

        layout = QVBoxLayout(containers_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # أزرار إدارة الحاويات
        buttons_layout = QHBoxLayout()

        self.add_container_button = QPushButton("إضافة حاوية")
        self.add_container_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.remove_container_button = QPushButton("حذف حاوية")
        self.remove_container_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.remove_container_button.setEnabled(False)

        self.edit_container_button = QPushButton("تعديل حاوية")
        self.edit_container_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
        """)
        self.edit_container_button.setEnabled(False)

        buttons_layout.addWidget(self.add_container_button)
        buttons_layout.addWidget(self.remove_container_button)
        buttons_layout.addWidget(self.edit_container_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # جدول الحاويات
        self.containers_table = QTableWidget()
        self.containers_table.setColumnCount(8)
        self.containers_table.setHorizontalHeaderLabels([
            "رقم الحاوية", "نوع الحاوية", "الحجم", "الوزن الفارغ",
            "الوزن المحمل", "الحالة", "تاريخ التحميل", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.containers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الحاوية
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # نوع الحاوية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الحجم
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الوزن الفارغ
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الوزن المحمل
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ التحميل
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # ملاحظات

        self.containers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.containers_table.setAlternatingRowColors(True)
        self.containers_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.containers_table)

        # معلومات إضافية عن الحاويات
        info_group = QGroupBox("معلومات إضافية")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #3498db;
                background-color: white;
            }
        """)
        info_layout = QFormLayout(info_group)
        info_layout.setSpacing(12)

        # إجمالي الوزن
        self.total_weight_spin = QDoubleSpinBox()
        self.total_weight_spin.setRange(0.00, 999999.99)
        self.total_weight_spin.setDecimals(2)
        self.total_weight_spin.setSuffix(" كجم")
        self.total_weight_spin.setStyleSheet(self.get_input_style())
        info_layout.addRow("إجمالي الوزن:", self.total_weight_spin)

        # إجمالي الحجم
        self.total_volume_spin = QDoubleSpinBox()
        self.total_volume_spin.setRange(0.00, 999999.99)
        self.total_volume_spin.setDecimals(2)
        self.total_volume_spin.setSuffix(" م³")
        self.total_volume_spin.setStyleSheet(self.get_input_style())
        info_layout.addRow("إجمالي الحجم:", self.total_volume_spin)

        # عدد الطرود
        self.packages_count_spin = QSpinBox()
        self.packages_count_spin.setRange(0, 999999)
        self.packages_count_spin.setStyleSheet(self.get_input_style())
        info_layout.addRow("عدد الطرود:", self.packages_count_spin)

        # نوع التعبئة
        self.packaging_type_combo = QComboBox()
        self.packaging_type_combo.addItems([
            "صناديق", "أكياس", "براميل", "لفائف", "منصات", "أخرى"
        ])
        self.packaging_type_combo.setStyleSheet(self.get_input_style())
        info_layout.addRow("نوع التعبئة:", self.packaging_type_combo)

        # ملاحظات الشحن
        self.shipping_notes_edit = QTextEdit()
        self.shipping_notes_edit.setMaximumHeight(80)
        self.shipping_notes_edit.setPlaceholderText("ملاحظات خاصة بالشحن...")
        self.shipping_notes_edit.setStyleSheet(self.get_input_style())
        info_layout.addRow("ملاحظات الشحن:", self.shipping_notes_edit)

        layout.addWidget(info_group)

        # إجمالي الحاويات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_containers_label = QLabel("إجمالي الحاويات: 0")
        self.total_containers_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_containers_label)

        layout.addLayout(totals_layout)

    def create_documents_tab(self):
        """إنشاء تبويب المستندات"""
        documents_tab = QWidget()
        self.tab_widget.addTab(documents_tab, "📄 المستندات")

        layout = QVBoxLayout(documents_tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # قسم روابط المستندات المحددة
        documents_links_group = QGroupBox("روابط المستندات")
        documents_links_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #9b59b6;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #9b59b6;
                background-color: white;
            }
        """)
        documents_links_layout = QGridLayout(documents_links_group)
        documents_links_layout.setSpacing(8)

        # المستندات الأولية
        self.initial_docs_label = QLabel("المستندات الأولية:")
        self.initial_docs_edit = QLineEdit()
        self.initial_docs_edit.setPlaceholderText("رابط المستندات الأولية")
        self.initial_docs_edit.setReadOnly(True)
        self.initial_docs_edit.setStyleSheet(self.get_input_style())
        self.initial_docs_button = QPushButton("رابط")
        self.initial_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 4px 8px; border-radius: 3px; font-size: 11px; }")
        self.initial_docs_attach_button = QPushButton("مرفق")
        self.initial_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 4px 8px; border-radius: 3px; font-size: 11px; }")

        documents_links_layout.addWidget(self.initial_docs_label, 0, 0)
        documents_links_layout.addWidget(self.initial_docs_edit, 0, 1)
        documents_links_layout.addWidget(self.initial_docs_button, 0, 2)
        documents_links_layout.addWidget(self.initial_docs_attach_button, 0, 3)

        # مستندات DN
        self.dn_docs_label = QLabel("المستندات (DN):")
        self.dn_docs_edit = QLineEdit()
        self.dn_docs_edit.setPlaceholderText("رابط مستندات DN")
        self.dn_docs_edit.setReadOnly(True)
        self.dn_docs_edit.setStyleSheet(self.get_input_style())
        self.dn_docs_button = QPushButton("إضافة رابط")
        self.dn_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.dn_docs_attach_button = QPushButton("إضافة مرفق")
        self.dn_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.dn_docs_label, 1, 0)
        documents_links_layout.addWidget(self.dn_docs_edit, 1, 1)
        documents_links_layout.addWidget(self.dn_docs_button, 1, 2)
        documents_links_layout.addWidget(self.dn_docs_attach_button, 1, 3)

        # المستندات المرسلة للجمارك
        self.customs_docs_label = QLabel("المستندات المرسلة للجمارك:")
        self.customs_docs_edit = QLineEdit()
        self.customs_docs_edit.setPlaceholderText("رابط المستندات المرسلة للجمارك")
        self.customs_docs_edit.setReadOnly(True)
        self.customs_docs_edit.setStyleSheet(self.get_input_style())
        self.customs_docs_button = QPushButton("إضافة رابط")
        self.customs_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.customs_docs_attach_button = QPushButton("إضافة مرفق")
        self.customs_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.customs_docs_label, 2, 0)
        documents_links_layout.addWidget(self.customs_docs_edit, 2, 1)
        documents_links_layout.addWidget(self.customs_docs_button, 2, 2)
        documents_links_layout.addWidget(self.customs_docs_attach_button, 2, 3)

        # بوليصة الشحن
        self.bill_lading_label = QLabel("بوليصة الشحن:")
        self.bill_lading_edit = QLineEdit()
        self.bill_lading_edit.setPlaceholderText("رابط بوليصة الشحن")
        self.bill_lading_edit.setReadOnly(True)
        self.bill_lading_edit.setStyleSheet(self.get_input_style())
        self.bill_lading_button = QPushButton("إضافة رابط")
        self.bill_lading_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.bill_lading_attach_button = QPushButton("إضافة مرفق")
        self.bill_lading_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.bill_lading_label, 3, 0)
        documents_links_layout.addWidget(self.bill_lading_edit, 3, 1)
        documents_links_layout.addWidget(self.bill_lading_button, 3, 2)
        documents_links_layout.addWidget(self.bill_lading_attach_button, 3, 3)

        # صور الأصناف
        self.items_images_label = QLabel("صور الأصناف:")
        self.items_images_edit = QLineEdit()
        self.items_images_edit.setPlaceholderText("رابط صور الأصناف")
        self.items_images_edit.setReadOnly(True)
        self.items_images_edit.setStyleSheet(self.get_input_style())
        self.items_images_button = QPushButton("إضافة رابط")
        self.items_images_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.items_images_attach_button = QPushButton("إضافة مرفق")
        self.items_images_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.items_images_label, 4, 0)
        documents_links_layout.addWidget(self.items_images_edit, 4, 1)
        documents_links_layout.addWidget(self.items_images_button, 4, 2)
        documents_links_layout.addWidget(self.items_images_attach_button, 4, 3)

        # مستندات أخرى
        self.other_docs_label = QLabel("مستندات أخرى:")
        self.other_docs_edit = QLineEdit()
        self.other_docs_edit.setPlaceholderText("رابط مستندات أخرى")
        self.other_docs_edit.setReadOnly(True)
        self.other_docs_edit.setStyleSheet(self.get_input_style())
        self.other_docs_button = QPushButton("إضافة رابط")
        self.other_docs_button.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; }")
        self.other_docs_attach_button = QPushButton("إضافة مرفق")
        self.other_docs_attach_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 5px 10px; border-radius: 3px; }")

        documents_links_layout.addWidget(self.other_docs_label, 5, 0)
        documents_links_layout.addWidget(self.other_docs_edit, 5, 1)
        documents_links_layout.addWidget(self.other_docs_button, 5, 2)
        documents_links_layout.addWidget(self.other_docs_attach_button, 5, 3)

        layout.addWidget(documents_links_group)

        # قسم المستندات الإضافية (الجدول القديم)
        additional_docs_group = QGroupBox("مستندات إضافية")
        additional_docs_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border: 3px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #e74c3c;
                background-color: white;
            }
        """)
        additional_docs_layout = QVBoxLayout(additional_docs_group)

        # أزرار إدارة المستندات الإضافية
        buttons_layout = QHBoxLayout()

        self.add_document_button = QPushButton("إضافة مستند")
        self.add_document_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        self.add_link_button = QPushButton("إضافة رابط")
        self.add_link_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        self.remove_document_button = QPushButton("حذف")
        self.remove_document_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.remove_document_button.setEnabled(False)

        self.open_document_button = QPushButton("فتح")
        self.open_document_button.setStyleSheet("QPushButton { background-color: #ff9800; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")
        self.open_document_button.setEnabled(False)

        self.add_attachment_button = QPushButton("إضافة مرفق")
        self.add_attachment_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 8px 15px; border-radius: 5px; font-weight: bold; }")

        buttons_layout.addWidget(self.add_document_button)
        buttons_layout.addWidget(self.add_link_button)
        buttons_layout.addWidget(self.add_attachment_button)
        buttons_layout.addWidget(self.remove_document_button)
        buttons_layout.addWidget(self.open_document_button)
        buttons_layout.addStretch()

        additional_docs_layout.addLayout(buttons_layout)

        # جدول المستندات
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels([
            "اسم المستند", "النوع", "المسار/الرابط", "تاريخ الإضافة", "ملاحظات"
        ])

        # إعداد الجدول
        header = self.documents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # اسم المستند
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المسار/الرابط
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # تاريخ الإضافة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # ملاحظات

        self.documents_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.documents_table.setAlternatingRowColors(True)
        self.documents_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        additional_docs_layout.addWidget(self.documents_table)

        # إجمالي المستندات
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        self.total_documents_label = QLabel("إجمالي المستندات: 0")
        self.total_documents_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)

        totals_layout.addWidget(self.total_documents_label)

        additional_docs_layout.addLayout(totals_layout)

        layout.addWidget(additional_docs_group)

    def create_dialog_buttons(self, main_layout):
        """إنشاء أزرار الحوار"""
        button_box = QDialogButtonBox()
        button_box.setStyleSheet("""
            QDialogButtonBox {
                background-color: #f8f9fa;
                border-top: 3px solid #dee2e6;
                padding: 20px;
                border-radius: 10px;
            }
            QPushButton {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        # زر حفظ وإغلاق
        save_close_btn = button_box.addButton("حفظ وإغلاق", QDialogButtonBox.AcceptRole)
        save_close_btn.clicked.connect(self.save_and_close)
        
        # زر إلغاء
        cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        cancel_btn.clicked.connect(self.reject)
        
        main_layout.addWidget(button_box)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.new_button.clicked.connect(self.new_shipment)
        self.save_button.clicked.connect(self.save_shipment)
        self.edit_button.clicked.connect(self.edit_shipment)
        self.exit_button.clicked.connect(self.reject)
        
    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        reply = QMessageBox.question(
            self,
            "شحنة جديدة",
            "هل تريد إنشاء شحنة جديدة؟\nسيتم مسح البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_form()
            self.current_shipment_id = None
            self.edit_button.setEnabled(False)
            QMessageBox.information(self, "شحنة جديدة", "✅ تم إنشاء نموذج شحنة جديد")
    
    def save_shipment(self):
        """حفظ الشحنة"""
        try:
            # محاكاة عملية الحفظ
            QMessageBox.information(self, "حفظ", "✅ تم حفظ الشحنة بنجاح")
            self.edit_button.setEnabled(True)
            self.current_shipment_id = 1  # محاكاة ID
            return True
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الشحنة:\n{str(e)}")
            return False
    
    def edit_shipment(self):
        """تعديل الشحنة"""
        QMessageBox.information(self, "تعديل", "✏️ يمكنك الآن تعديل الشحنة")
    
    def save_and_close(self):
        """حفظ وإغلاق النافذة"""
        if self.save_shipment():
            self.accept()
    
    def clear_form(self):
        """مسح جميع الحقول"""
        try:
            # تبويب البيانات الأساسية
            self.shipment_number_edit.clear()
            self.supplier_edit.clear()
            self.supplier_invoice_edit.clear()
            self.shipment_status_combo.setCurrentIndex(0)
            self.clearance_status_combo.setCurrentIndex(0)
            self.tracking_number_edit.clear()
            self.bill_of_lading_edit.clear()
            self.notes_edit.clear()

            # تبويب الأصناف
            self.items_table.setRowCount(0)
            self.total_items_label.setText("إجمالي الأصناف: 0")
            self.total_value_label.setText("إجمالي القيمة: 0.00")

            # تبويب المالية
            self.currency_combo.setCurrentIndex(0)
            self.exchange_rate_spin.setValue(1.00)
            self.goods_value_spin.setValue(0.00)
            self.shipping_cost_spin.setValue(0.00)
            self.insurance_cost_spin.setValue(0.00)
            self.customs_fees_spin.setValue(0.00)
            self.other_fees_spin.setValue(0.00)
            self.total_costs_spin.setValue(0.00)
            self.total_local_currency_spin.setValue(0.00)
            self.payment_status_combo.setCurrentIndex(0)
            self.paid_amount_spin.setValue(0.00)
            self.remaining_amount_spin.setValue(0.00)
            self.payment_date_edit.setDate(QDate.currentDate())
            self.payment_method_combo.setCurrentIndex(0)

            # تبويب الشحن
            self.shipping_company_edit.clear()
            self.shipping_type_combo.setCurrentIndex(0)
            self.shipping_method_combo.setCurrentIndex(0)
            self.incoterms_combo.setCurrentIndex(0)
            self.port_of_loading_edit.clear()
            self.port_of_discharge_edit.clear()
            self.final_destination_edit.clear()
            self.vessel_name_edit.clear()
            self.voyage_number_edit.clear()
            self.bill_of_lading_number_edit.clear()
            self.estimated_departure_date_edit.setDate(QDate.currentDate())
            self.actual_departure_date_edit.setDate(QDate.currentDate())
            self.estimated_arrival_date_edit.setDate(QDate.currentDate())
            self.actual_arrival_date_edit.setDate(QDate.currentDate())

            # تبويب الحاويات
            self.containers_table.setRowCount(0)
            self.total_weight_spin.setValue(0.00)
            self.total_volume_spin.setValue(0.00)
            self.packages_count_spin.setValue(0)
            self.packaging_type_combo.setCurrentIndex(0)
            self.shipping_notes_edit.clear()
            self.total_containers_label.setText("إجمالي الحاويات: 0")

            # تبويب المستندات
            self.initial_docs_edit.clear()
            self.dn_docs_edit.clear()
            self.customs_docs_edit.clear()
            self.bill_lading_edit.clear()
            self.items_images_edit.clear()
            self.other_docs_edit.clear()
            self.documents_table.setRowCount(0)
            self.total_documents_label.setText("إجمالي المستندات: 0")

            print("✅ تم مسح النموذج بالكامل")
        except Exception as e:
            print(f"خطأ في مسح النموذج: {str(e)}")

def main():
    """اختبار النافذة النهائية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = NewShipmentWindow()
    window.show()
    
    print("✅ تم فتح النافذة النهائية مع أزرار التحكم")
    print("🔍 تحقق من وجود:")
    print("   • عنوان أزرق في الأعلى")
    print("   • شريط أزرار ملون: إضافة (أزرق)، حفظ (أخضر)، تعديل (برتقالي)، خروج (أحمر)")
    print("   • تبويبات: البيانات الأساسية، الأصناف، البيانات المالية")
    print("   • أزرار حفظ وإغلاق/إلغاء في الأسفل")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
