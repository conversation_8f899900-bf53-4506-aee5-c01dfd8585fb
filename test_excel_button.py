#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_excel_button():
    """اختبار زر استيراد Excel"""
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from src.ui.shipments.new_shipment_window import NewShipmentWindow

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        print("🧪 اختبار زر استيراد Excel...")
        window = NewShipmentWindow()
        
        # اختبار وجود الزر
        if hasattr(window, 'import_excel_button'):
            print("✅ زر import_excel_button موجود")
            print(f"   النص: {window.import_excel_button.text()}")
            print(f"   مرئي: {window.import_excel_button.isVisible()}")
            print(f"   مفعل: {window.import_excel_button.isEnabled()}")
        else:
            print("❌ زر import_excel_button غير موجود")
        
        # اختبار وجود الوظيفة
        if hasattr(window, 'import_from_excel'):
            print("✅ وظيفة import_from_excel موجودة")
        else:
            print("❌ وظيفة import_from_excel غير موجودة")
        
        # اختبار استيراد الحوار
        try:
            from src.ui.dialogs.excel_import_dialog import ExcelImportDialog
            print("✅ تم استيراد ExcelImportDialog بنجاح")
        except Exception as e:
            print(f"❌ فشل في استيراد ExcelImportDialog: {e}")
        
        # عرض النافذة للاختبار البصري
        window.show()
        print("🔍 تم فتح النافذة - تحقق من وجود زر 'استيراد Excel' في شريط الأدوات")
        
        app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_excel_button()
