#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهة الجديدة المحسنة
Enhanced UI Test
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame, QGridLayout, QScrollArea, QGraphicsDropShadowEffect
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor

class ModernMainWindow(QMainWindow):
    """نافذة رئيسية حديثة للاختبار"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚢 نظام إدارة الشحنات المتكامل - الواجهة المحسنة")
        self.setMinimumSize(1200, 800)
        self.showMaximized()
        
        # إعداد الستايل
        self.setup_style()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # إعداد المؤقت
        self.setup_timer()
    
    def setup_style(self):
        """إعداد الستايل العام"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:0.5 #e9ecef, stop:1 #dee2e6);
                font-family: "Segoe UI", "Arial", sans-serif;
            }
            
            QScrollArea {
                border: none;
                background: transparent;
            }
            
            QScrollBar:vertical {
                background: #ecf0f1;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: #3498db;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #2980b9;
            }
        """)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        central_widget = QWidget()
        scroll_area.setWidget(central_widget)
        self.setCentralWidget(scroll_area)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # الهيدر
        self.create_header(main_layout)
        
        # قسم الإحصائيات
        self.create_stats_section(main_layout)
        
        # الأزرار الرئيسية
        self.create_buttons_section(main_layout)
        
        # قسم الأخبار
        self.create_news_section(main_layout)
        
        main_layout.addStretch()
    
    def create_header(self, layout):
        """إنشاء الهيدر"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 20px;
                padding: 20px;
            }
        """)
        
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 5)
        header_frame.setGraphicsEffect(shadow)
        
        header_layout = QVBoxLayout(header_frame)
        
        # العنوان الرئيسي
        title_label = QLabel("🚢 نظام إدارة الشحنات المتكامل")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(28)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white; background: transparent; padding: 10px;")
        header_layout.addWidget(title_label)
        
        # العنوان الفرعي
        subtitle_label = QLabel("إدارة شاملة ومتطورة لجميع عمليات الشحن والتوريد")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(14)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); background: transparent; padding: 5px;")
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
    
    def create_stats_section(self, layout):
        """إنشاء قسم الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
            }
        """)
        
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 3)
        stats_frame.setGraphicsEffect(shadow)
        
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(20)
        stats_layout.setContentsMargins(25, 20, 25, 20)
        
        # بيانات الإحصائيات
        stats_data = [
            ("📦", "إجمالي الشحنات", "1,234", "#3498db"),
            ("🏭", "الموردين النشطين", "89", "#2ecc71"),
            ("📋", "الأصناف المسجلة", "5,678", "#e74c3c"),
            ("⏱️", "الشحنات المعلقة", "23", "#f39c12")
        ]
        
        for icon, title, value, color in stats_data:
            stat_widget = self.create_stat_widget(icon, title, value, color)
            stats_layout.addWidget(stat_widget)
        
        layout.addWidget(stats_frame)
    
    def create_stat_widget(self, icon, title, value, color):
        """إنشاء ويدجت إحصائية"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 12px;
                padding: 15px;
            }}
            QFrame:hover {{
                background: {self.darken_color(color)};
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setSpacing(8)
        layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(24)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("color: white; background: transparent;")
        layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_font = QFont()
        value_font.setPointSize(20)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet("color: white; background: transparent;")
        layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(10)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); background: transparent;")
        layout.addWidget(title_label)
        
        return widget
    
    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            "#3498db": "#2980b9",
            "#2ecc71": "#27ae60", 
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22"
        }
        return color_map.get(color, color)
    
    def create_buttons_section(self, layout):
        """إنشاء قسم الأزرار"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 3)
        buttons_frame.setGraphicsEffect(shadow)
        
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(20)
        
        # عنوان القسم
        section_title = QLabel("🎛️ الأنظمة الرئيسية")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        section_title.setFont(title_font)
        section_title.setStyleSheet("color: #2c3e50; background: transparent; padding: 10px;")
        buttons_layout.addWidget(section_title)
        
        # شبكة الأزرار
        grid_layout = QGridLayout()
        grid_layout.setSpacing(20)
        
        # بيانات الأزرار
        buttons_data = [
            ("⚙️", "الإعدادات العامة", "#9b59b6", 0, 0),
            ("📦", "إدارة الأصناف", "#3498db", 0, 1),
            ("🏭", "إدارة الموردين", "#2ecc71", 0, 2),
            ("🚢", "إدارة الشحنات", "#e74c3c", 1, 0),
            ("🏛️", "الإدخالات الجمركية", "#f39c12", 1, 1),
            ("💰", "إدارة التكاليف", "#1abc9c", 1, 2),
        ]
        
        for icon, text, color, row, col in buttons_data:
            button = self.create_main_button(icon, text, color)
            grid_layout.addWidget(button, row, col)
        
        buttons_layout.addLayout(grid_layout)
        layout.addWidget(buttons_frame)
    
    def create_main_button(self, icon, text, color):
        """إنشاء زر رئيسي"""
        button = QPushButton()
        button.setMinimumSize(280, 120)
        button.setCursor(Qt.PointingHandCursor)
        
        # التخطيط الداخلي
        button_layout = QVBoxLayout()
        button_layout.setSpacing(8)
        button_layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(28)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("background: transparent; color: white;")
        
        # النص
        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        text_font = QFont()
        text_font.setPointSize(12)
        text_font.setBold(True)
        text_label.setFont(text_font)
        text_label.setStyleSheet("background: transparent; color: white;")
        
        button_layout.addWidget(icon_label)
        button_layout.addWidget(text_label)
        
        inner_widget = QWidget()
        inner_widget.setLayout(button_layout)
        
        main_layout = QVBoxLayout(button)
        main_layout.addWidget(inner_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # الستايل
        hover_color = self.darken_color(color)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {hover_color});
                border: none;
                border-radius: 15px;
                padding: 20px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {hover_color}, stop:1 {color});
            }}
            QPushButton:pressed {{
                background: {hover_color};
            }}
        """)
        
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 5)
        button.setGraphicsEffect(shadow)
        
        return button
    
    def create_news_section(self, layout):
        """إنشاء قسم الأخبار"""
        news_frame = QFrame()
        news_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
                padding: 20px;
            }
        """)
        
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 3)
        news_frame.setGraphicsEffect(shadow)
        
        news_layout = QVBoxLayout(news_frame)
        news_layout.setSpacing(15)
        
        # عنوان القسم
        section_title = QLabel("📢 آخر التحديثات")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        section_title.setFont(title_font)
        section_title.setStyleSheet("color: #2c3e50; background: transparent; padding: 10px;")
        news_layout.addWidget(section_title)
        
        # قائمة الأخبار
        news_items = [
            "✅ تم تطوير واجهة رئيسية جديدة أكثر حيوية وجمالاً",
            "🎨 إضافة ثيمات متعددة وألوان متدرجة جذابة",
            "📊 تحسين عرض الإحصائيات مع أيقونات ملونة",
            "🔧 إضافة نظام إدارة الستايلات والثيمات"
        ]
        
        for news in news_items:
            news_label = QLabel(news)
            news_label.setStyleSheet("""
                QLabel {
                    color: #34495e;
                    background: #f8f9fa;
                    padding: 12px;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                    font-size: 12px;
                }
            """)
            news_layout.addWidget(news_label)
        
        layout.addWidget(news_frame)
    
    def setup_timer(self):
        """إعداد المؤقت"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)
    
    def update_status(self):
        """تحديث الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime('%Y/%m/%d - %H:%M:%S')
        self.statusBar().showMessage(f"🟢 النظام جاهز | 👤 المستخدم: مدير النظام | 📅 {current_time}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق
    app.setApplicationName("نظام إدارة الشحنات المتكامل")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("ProShipment")
    
    # إنشاء النافذة الرئيسية
    window = ModernMainWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
