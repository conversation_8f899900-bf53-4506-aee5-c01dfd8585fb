#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدخال السعر والكمية للأصناف
"""

import sys
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, 
    QPushButton, QLineEdit, QDoubleSpinBox, QSpinBox, QGroupBox,
    QDialogButtonBox, QMessageBox, QTextEdit
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap, QIcon

class ItemPriceDialog(QDialog):
    """نافذة إدخال السعر والكمية للصنف"""
    
    def __init__(self, parent=None, item_data=None):
        super().__init__(parent)
        self.item_data = item_data or {}
        self.quantity = 0
        self.unit_price = 0.0
        self.total_price = 0.0
        self.notes = ""
        self.setup_ui()
        self.setup_connections()
        
        # إذا كان هناك بيانات موجودة، املأ الحقول
        if item_data:
            self.load_item_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        item_name = self.item_data.get('name', 'صنف غير محدد')
        self.setWindowTitle(f"إدخال السعر والكمية - {item_name}")
        self.setModal(True)
        self.resize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel(f"إدخال بيانات الصنف")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # مجموعة بيانات الصنف
        item_info_group = QGroupBox("معلومات الصنف")
        item_info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #3498db;
                background-color: white;
            }
        """)
        item_info_layout = QFormLayout(item_info_group)
        
        # اسم الصنف
        self.item_name_label = QLabel(item_name)
        self.item_name_label.setStyleSheet("QLabel { font-weight: bold; color: #2c3e50; }")
        item_info_layout.addRow("اسم الصنف:", self.item_name_label)
        
        # كود الصنف
        item_code = self.item_data.get('code', 'غير محدد')
        self.item_code_label = QLabel(item_code)
        self.item_code_label.setStyleSheet("QLabel { color: #7f8c8d; }")
        item_info_layout.addRow("كود الصنف:", self.item_code_label)
        
        # وحدة القياس
        unit = self.item_data.get('unit', 'قطعة')
        self.unit_label = QLabel(unit)
        self.unit_label.setStyleSheet("QLabel { color: #7f8c8d; }")
        item_info_layout.addRow("وحدة القياس:", self.unit_label)
        
        main_layout.addWidget(item_info_group)
        
        # مجموعة إدخال البيانات
        input_group = QGroupBox("إدخال الكمية والسعر")
        input_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #27ae60;
                background-color: white;
            }
        """)
        input_layout = QFormLayout(input_group)
        
        # الكمية
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(999999)
        self.quantity_spin.setValue(1)
        self.quantity_spin.setStyleSheet(self.get_input_style())
        input_layout.addRow("الكمية:", self.quantity_spin)
        
        # السعر الوحدة (مع دعم الفاصلة العشرية)
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setMinimum(0.01)
        self.unit_price_spin.setMaximum(999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setValue(1.00)
        self.unit_price_spin.setStyleSheet(self.get_input_style())
        # دعم الفاصلة العشرية
        self.unit_price_spin.setLocale(self.unit_price_spin.locale())
        input_layout.addRow("سعر الوحدة:", self.unit_price_spin)
        
        # المجموع (للقراءة فقط)
        self.total_price_label = QLabel("1.00")
        self.total_price_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
                color: #495057;
            }
        """)
        input_layout.addRow("المجموع:", self.total_price_label)
        
        main_layout.addWidget(input_group)
        
        # مجموعة الملاحظات
        notes_group = QGroupBox("ملاحظات (اختياري)")
        notes_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #f39c12;
                background-color: white;
            }
        """)
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات خاصة بهذا الصنف...")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 11px;
            }
            QTextEdit:focus {
                border-color: #f39c12;
            }
        """)
        notes_layout.addWidget(self.notes_edit)
        
        main_layout.addWidget(notes_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        button_box.setLayoutDirection(Qt.RightToLeft)
        
        # زر موافق
        ok_button = button_box.addButton("موافق", QDialogButtonBox.AcceptRole)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        
        # زر إلغاء
        cancel_button = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        
        button_box.accepted.connect(self.accept_data)
        button_box.rejected.connect(self.reject)
        
        main_layout.addWidget(button_box)
    
    def get_input_style(self):
        """الحصول على تنسيق الحقول"""
        return """
            QSpinBox, QDoubleSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
            }
        """
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        # تحديث المجموع عند تغيير الكمية أو السعر
        self.quantity_spin.valueChanged.connect(self.update_total)
        self.unit_price_spin.valueChanged.connect(self.update_total)
    
    def update_total(self):
        """تحديث المجموع"""
        try:
            quantity = self.quantity_spin.value()
            unit_price = self.unit_price_spin.value()
            total = quantity * unit_price
            self.total_price_label.setText(f"{total:.2f}")
        except Exception as e:
            print(f"خطأ في حساب المجموع: {str(e)}")
    
    def load_item_data(self):
        """تحميل بيانات الصنف الموجودة"""
        try:
            if 'quantity' in self.item_data:
                self.quantity_spin.setValue(int(self.item_data['quantity']))
            if 'unit_price' in self.item_data:
                self.unit_price_spin.setValue(float(self.item_data['unit_price']))
            if 'notes' in self.item_data:
                self.notes_edit.setPlainText(str(self.item_data['notes']))
            
            # تحديث المجموع
            self.update_total()
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات الصنف: {str(e)}")
    
    def accept_data(self):
        """قبول البيانات والتحقق من صحتها"""
        try:
            # التحقق من صحة البيانات
            if self.quantity_spin.value() <= 0:
                QMessageBox.warning(self, "خطأ", "يجب أن تكون الكمية أكبر من صفر")
                return
            
            if self.unit_price_spin.value() <= 0:
                QMessageBox.warning(self, "خطأ", "يجب أن يكون السعر أكبر من صفر")
                return
            
            # حفظ البيانات
            self.quantity = self.quantity_spin.value()
            self.unit_price = self.unit_price_spin.value()
            self.total_price = self.quantity * self.unit_price
            self.notes = self.notes_edit.toPlainText().strip()
            
            # قبول الحوار
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ البيانات: {str(e)}")
    
    def get_item_data(self):
        """الحصول على بيانات الصنف المدخلة"""
        return {
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'total_price': self.total_price,
            'notes': self.notes
        }
