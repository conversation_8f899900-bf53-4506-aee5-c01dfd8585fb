# -*- coding: utf-8 -*-
"""
نافذة تتبع الشحنات
Shipment Tracking Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
                               QGroupBox, QFormLayout, QDateEdit, QToolBar, QStatusBar,
                               QProgressBar, QFrame, QGridLayout, QTextEdit)
from PySide6.QtCore import Qt, QDate, QTimer, Signal
from PySide6.QtGui import QAction, QIcon, QFont, QColor

from ...database.database_manager import DatabaseManager
from ...database.models import Shipment, Supplier
from ...utils.formatters import format_date, format_datetime, format_number, format_currency

class ShipmentTrackingWindow(QMainWindow):
    """نافذة تتبع الشحنات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.setup_toolbar()
        self.setup_connections()
        self.load_tracking_data()
        
        # تحديث تلقائي كل 30 ثانية
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_tracking_data)
        self.refresh_timer.start(30000)  # 30 ثانية
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تتبع الشحنات")
        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # إحصائيات سريعة
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # منطقة البحث والفلترة
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QFormLayout(search_group)
        
        # البحث بالنص
        search_text_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث برقم الشحنة أو رقم التتبع...")
        self.search_button = QPushButton("بحث")
        self.clear_search_button = QPushButton("مسح")
        
        search_text_layout.addWidget(self.search_edit)
        search_text_layout.addWidget(self.search_button)
        search_text_layout.addWidget(self.clear_search_button)
        search_layout.addRow("البحث:", search_text_layout)
        
        # فلترة بحالة الشحنة
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItem("جميع الحالات", "")
        self.status_filter_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق", 
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        search_layout.addRow("حالة الشحنة:", self.status_filter_combo)
        
        main_layout.addWidget(search_group)
        
        # جدول تتبع الشحنات
        self.tracking_table = QTableWidget()
        self.tracking_table.setColumnCount(17)  # زيادة عدد الأعمدة لإضافة رقم الحاوية
        self.tracking_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "المورد", "رقم فاتورة المورد", "حالة الشحنة", "حالة الإفراج",
            "رقم التتبع", "بوليصة الشحن", "رقم الحاوية", "شركة الشحن", "طريقة الشحن",
            "ميناء التحميل", "ميناء التفريغ", "الوجهة النهائية", "اسم السفينة", "رقم الرحلة",
            "تاريخ المغادرة", "تاريخ الوصول المتوقع"
        ])
        
        # إعداد الجدول
        header = self.tracking_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الشحنة
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # المورد - حجم ثابت مقلص
        header.resizeSection(1, 100)  # تقليص عمود المورد إلى 100 بكسل
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # رقم فاتورة المورد
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # حالة الشحنة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # حالة الإفراج
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # رقم التتبع
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # بوليصة الشحن
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # رقم الحاوية
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # شركة الشحن
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # طريقة الشحن
        header.setSectionResizeMode(10, QHeaderView.ResizeToContents)  # ميناء التحميل
        header.setSectionResizeMode(11, QHeaderView.ResizeToContents)  # ميناء التفريغ
        header.setSectionResizeMode(12, QHeaderView.ResizeToContents)  # الوجهة النهائية
        header.setSectionResizeMode(13, QHeaderView.ResizeToContents)  # اسم السفينة
        header.setSectionResizeMode(14, QHeaderView.ResizeToContents)  # رقم الرحلة
        header.setSectionResizeMode(15, QHeaderView.ResizeToContents)  # تاريخ المغادرة
        header.setSectionResizeMode(16, QHeaderView.ResizeToContents)  # تاريخ الوصول المتوقع
        
        self.tracking_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tracking_table.setAlternatingRowColors(True)
        # جعل الجدول للقراءة فقط (غير قابل للتعديل)
        self.tracking_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        main_layout.addWidget(self.tracking_table)
        
        # منطقة تفاصيل الشحنة المحددة
        details_group = QGroupBox("تفاصيل الشحنة")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setReadOnly(True)
        details_layout.addWidget(self.details_text)
        
        main_layout.addWidget(details_group)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_frame.setMaximumHeight(120)
        
        stats_layout = QGridLayout(stats_frame)
        
        # إحصائيات مختلفة
        self.total_shipments_label = self.create_stat_label("إجمالي الشحنات", "0", "#2196F3")
        self.in_transit_label = self.create_stat_label("في الطريق", "0", "#FF9800")
        self.delivered_label = self.create_stat_label("تم التسليم", "0", "#4CAF50")
        self.delayed_label = self.create_stat_label("متأخرة", "0", "#F44336")
        self.at_port_label = self.create_stat_label("في الميناء", "0", "#9C27B0")
        self.in_customs_label = self.create_stat_label("في الجمارك", "0", "#607D8B")
        
        stats_layout.addWidget(self.total_shipments_label, 0, 0)
        stats_layout.addWidget(self.in_transit_label, 0, 1)
        stats_layout.addWidget(self.delivered_label, 0, 2)
        stats_layout.addWidget(self.delayed_label, 1, 0)
        stats_layout.addWidget(self.at_port_label, 1, 1)
        stats_layout.addWidget(self.in_customs_label, 1, 2)
        
        return stats_frame
        
    def create_stat_label(self, title, value, color):
        """إنشاء تسمية إحصائية"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setStyleSheet(f"QFrame {{ border-left: 4px solid {color}; background-color: #f5f5f5; }}")
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 12px; color: #666;")
        
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"font-size: 24px; font-weight: bold; color: {color};")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        # حفظ مرجع لتسمية القيمة للتحديث لاحقاً
        frame.value_label = value_label
        
        return frame
        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar("أدوات التتبع")
        self.addToolBar(toolbar)
        
        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setStatusTip("تحديث بيانات التتبع")
        refresh_action.triggered.connect(self.load_tracking_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # تصدير
        export_action = QAction("تصدير", self)
        export_action.setStatusTip("تصدير بيانات التتبع")
        export_action.triggered.connect(self.export_tracking_data)
        toolbar.addAction(export_action)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_button.clicked.connect(self.search_shipments)
        self.clear_search_button.clicked.connect(self.clear_search)
        self.search_edit.returnPressed.connect(self.search_shipments)
        
        # فلترة عند تغيير الكومبو بوكس
        self.status_filter_combo.currentTextChanged.connect(self.filter_shipments)
        
        # عرض التفاصيل عند تحديد شحنة
        self.tracking_table.itemSelectionChanged.connect(self.show_shipment_details)
        
    def load_tracking_data(self):
        """تحميل بيانات التتبع"""
        session = self.db_manager.get_session()
        try:
            shipments = session.query(Shipment).join(Supplier).filter(
                Shipment.is_active == True
            ).order_by(Shipment.created_at.desc()).all()
            
            self.populate_tracking_table(shipments)
            self.update_statistics(shipments)
            self.status_bar.showMessage(f"تم تحديث بيانات {len(shipments)} شحنة - آخر تحديث: {QDate.currentDate().toString('yyyy-MM-dd')}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات التتبع: {str(e)}")
        finally:
            session.close()
            
    def populate_tracking_table(self, shipments):
        """ملء جدول التتبع"""
        self.tracking_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            # رقم الشحنة
            number_item = QTableWidgetItem(shipment.shipment_number or "")
            number_item.setData(Qt.UserRole, shipment.id)
            self.tracking_table.setItem(row, 0, number_item)

            # المورد (مقلص)
            supplier_name = shipment.supplier.name if shipment.supplier else ""
            # تقليص اسم المورد إذا كان طويلاً
            if len(supplier_name) > 15:
                supplier_name = supplier_name[:12] + "..."
            supplier_item = QTableWidgetItem(supplier_name)
            supplier_item.setToolTip(shipment.supplier.name if shipment.supplier else "")  # عرض الاسم الكامل في tooltip
            self.tracking_table.setItem(row, 1, supplier_item)

            # رقم فاتورة المورد
            invoice_item = QTableWidgetItem(shipment.supplier_invoice_number or "")
            self.tracking_table.setItem(row, 2, invoice_item)

            # حالة الشحنة مع لون
            status_item = QTableWidgetItem(shipment.shipment_status or "")
            status_item.setBackground(self.get_status_color(shipment.shipment_status))
            self.tracking_table.setItem(row, 3, status_item)

            # حالة الإفراج
            clearance_item = QTableWidgetItem(shipment.clearance_status or "")
            self.tracking_table.setItem(row, 4, clearance_item)

            # رقم التتبع
            tracking_item = QTableWidgetItem(shipment.tracking_number or "")
            self.tracking_table.setItem(row, 5, tracking_item)

            # بوليصة الشحن
            bl_item = QTableWidgetItem(shipment.bill_of_lading or "")
            self.tracking_table.setItem(row, 6, bl_item)

            # رقم الحاوية (العمود الجديد)
            container_item = QTableWidgetItem(shipment.container_number or "")
            self.tracking_table.setItem(row, 7, container_item)

            # شركة الشحن (تم تحريك الفهرس)
            shipping_company_item = QTableWidgetItem(shipment.shipping_company or "")
            self.tracking_table.setItem(row, 8, shipping_company_item)

            # طريقة الشحن (تم تحريك الفهرس)
            shipping_method_item = QTableWidgetItem(shipment.shipping_method or "")
            self.tracking_table.setItem(row, 9, shipping_method_item)

            # ميناء التحميل (تم تحريك الفهرس)
            loading_port_item = QTableWidgetItem(shipment.port_of_loading or "")
            self.tracking_table.setItem(row, 10, loading_port_item)

            # ميناء التفريغ (تم تحريك الفهرس)
            discharge_port_item = QTableWidgetItem(shipment.port_of_discharge or "")
            self.tracking_table.setItem(row, 11, discharge_port_item)

            # الوجهة النهائية (تم تحريك الفهرس)
            destination_item = QTableWidgetItem(shipment.port_of_discharge or "")
            self.tracking_table.setItem(row, 12, destination_item)

            # اسم السفينة (تم تحريك الفهرس)
            vessel_item = QTableWidgetItem(shipment.vessel_name or "")
            self.tracking_table.setItem(row, 13, vessel_item)

            # رقم الرحلة (تم تحريك الفهرس)
            voyage_item = QTableWidgetItem(shipment.voyage_number or "")
            self.tracking_table.setItem(row, 14, voyage_item)

            # تاريخ المغادرة (تم تحريك الفهرس) - بتنسيق الويندوز
            departure_date = format_date(shipment.actual_departure_date, 'short') if shipment.actual_departure_date else ""
            departure_item = QTableWidgetItem(departure_date)
            self.tracking_table.setItem(row, 15, departure_item)

            # تاريخ الوصول المتوقع (تم تحريك الفهرس) - بتنسيق الويندوز
            est_arrival = format_date(shipment.estimated_arrival_date, 'short') if shipment.estimated_arrival_date else ""
            est_arrival_item = QTableWidgetItem(est_arrival)
            self.tracking_table.setItem(row, 16, est_arrival_item)
            
    def get_status_color(self, status):
        """الحصول على لون الحالة"""
        colors = {
            "تحت الطلب": QColor("#FFF3E0"),
            "مؤكدة": QColor("#E3F2FD"),
            "تم الشحن": QColor("#E8F5E8"),
            "في الطريق": QColor("#FFF8E1"),
            "وصلت الميناء": QColor("#F3E5F5"),
            "في الجمارك": QColor("#ECEFF1"),
            "تم التسليم": QColor("#E8F5E8"),
            "ملغية": QColor("#FFEBEE"),
            "متاخرة": QColor("#FFCDD2")
        }
        return colors.get(status, QColor("#FFFFFF"))
        
    def calculate_progress(self, status):
        """حساب نسبة التقدم"""
        progress_map = {
            "تحت الطلب": 10,
            "مؤكدة": 20,
            "تم الشحن": 40,
            "في الطريق": 60,
            "وصلت الميناء": 80,
            "في الجمارك": 90,
            "تم التسليم": 100,
            "ملغية": 0,
            "متاخرة": 30
        }
        return progress_map.get(status, 0)
        
    def update_statistics(self, shipments):
        """تحديث الإحصائيات"""
        total = len(shipments)
        in_transit = len([s for s in shipments if s.shipment_status == "في الطريق"])
        delivered = len([s for s in shipments if s.shipment_status == "تم التسليم"])
        delayed = len([s for s in shipments if s.shipment_status == "متاخرة"])
        at_port = len([s for s in shipments if s.shipment_status == "وصلت الميناء"])
        in_customs = len([s for s in shipments if s.shipment_status == "في الجمارك"])
        
        self.total_shipments_label.value_label.setText(str(total))
        self.in_transit_label.value_label.setText(str(in_transit))
        self.delivered_label.value_label.setText(str(delivered))
        self.delayed_label.value_label.setText(str(delayed))
        self.at_port_label.value_label.setText(str(at_port))
        self.in_customs_label.value_label.setText(str(in_customs))
        
    def show_shipment_details(self):
        """عرض تفاصيل الشحنة المحددة"""
        current_row = self.tracking_table.currentRow()
        if current_row >= 0:
            shipment_id = self.tracking_table.item(current_row, 0).data(Qt.UserRole)
            
            session = self.db_manager.get_session()
            try:
                shipment = session.query(Shipment).filter(Shipment.id == shipment_id).first()
                if shipment:
                    details = f"""
رقم الشحنة: {shipment.shipment_number}
المورد: {shipment.supplier.name if shipment.supplier else 'غير محدد'}
رقم فاتورة المورد: {shipment.supplier_invoice_number or 'غير محدد'}
حالة الشحنة: {shipment.shipment_status}
حالة الإفراج: {shipment.clearance_status}
رقم التتبع: {shipment.tracking_number or 'غير محدد'}
شركة الشحن: {shipment.shipping_company or 'غير محدد'}
اسم السفينة: {shipment.vessel_name or 'غير محدد'}
رقم الرحلة: {shipment.voyage_number or 'غير محدد'}
ميناء التحميل: {shipment.port_of_loading or 'غير محدد'}
ميناء التفريغ: {shipment.port_of_discharge or 'غير محدد'}
ملاحظات: {shipment.notes or 'لا توجد ملاحظات'}
                    """
                    self.details_text.setPlainText(details.strip())
            except Exception as e:
                self.details_text.setPlainText(f"خطأ في تحميل التفاصيل: {str(e)}")
            finally:
                session.close()
        else:
            self.details_text.clear()
            
    def search_shipments(self):
        """البحث في الشحنات"""
        # TODO: تنفيذ البحث
        pass
        
    def filter_shipments(self):
        """فلترة الشحنات"""
        # TODO: تنفيذ الفلترة
        pass
        
    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.status_filter_combo.setCurrentIndex(0)
        self.load_tracking_data()
        
    def export_tracking_data(self):
        """تصدير بيانات التتبع"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ ميزة التصدير لاحقاً")
        
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.refresh_timer.stop()
        event.accept()
