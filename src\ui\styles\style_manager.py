# -*- coding: utf-8 -*-
"""
مدير الستايلات والثيمات
Style and Theme Manager
"""

import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QFile, QTextStream

class StyleManager:
    """مدير الستايلات والثيمات"""
    
    def __init__(self):
        self.styles_dir = Path(__file__).parent
        self.current_theme = "modern"
    
    def load_theme(self, theme_name="modern"):
        """تحميل ثيم محدد"""
        try:
            theme_file = self.styles_dir / f"{theme_name}_theme.qss"
            
            if not theme_file.exists():
                print(f"ملف الثيم غير موجود: {theme_file}")
                return False
            
            with open(theme_file, 'r', encoding='utf-8') as file:
                style_sheet = file.read()
            
            # تطبيق الستايل على التطبيق
            app = QApplication.instance()
            if app:
                app.setStyleSheet(style_sheet)
                self.current_theme = theme_name
                print(f"تم تحميل ثيم: {theme_name}")
                return True
            
        except Exception as e:
            print(f"خطأ في تحميل الثيم: {e}")
            return False
        
        return False
    
    def get_available_themes(self):
        """الحصول على قائمة الثيمات المتاحة"""
        themes = []
        for file in self.styles_dir.glob("*_theme.qss"):
            theme_name = file.stem.replace("_theme", "")
            themes.append(theme_name)
        return themes
    
    def get_current_theme(self):
        """الحصول على الثيم الحالي"""
        return self.current_theme
    
    def create_gradient_style(self, start_color, end_color, direction="vertical"):
        """إنشاء ستايل متدرج"""
        if direction == "vertical":
            gradient = f"qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {start_color}, stop:1 {end_color})"
        else:  # horizontal
            gradient = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {start_color}, stop:1 {end_color})"
        
        return gradient
    
    def create_button_style(self, bg_color, hover_color, text_color="white", border_radius=8):
        """إنشاء ستايل زر مخصص"""
        return f"""
            QPushButton {{
                background: {bg_color};
                color: {text_color};
                border: none;
                border-radius: {border_radius}px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background: {hover_color};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: {hover_color};
                transform: translateY(0px);
            }}
        """
    
    def create_card_style(self, bg_color="white", border_color="#e0e0e0", shadow=True):
        """إنشاء ستايل كارت"""
        shadow_style = ""
        if shadow:
            shadow_style = "box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);"
        
        return f"""
            QFrame {{
                background: {bg_color};
                border: 1px solid {border_color};
                border-radius: 12px;
                padding: 15px;
                {shadow_style}
            }}
        """
    
    def apply_dark_theme(self):
        """تطبيق الثيم المظلم"""
        dark_style = """
            QMainWindow {
                background: #2b2b2b;
                color: #ffffff;
            }
            
            QWidget {
                background: #2b2b2b;
                color: #ffffff;
            }
            
            QPushButton {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background: #505050;
                border-color: #777777;
            }
            
            QPushButton:pressed {
                background: #353535;
            }
            
            QLineEdit, QTextEdit, QPlainTextEdit {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                border-color: #3498db;
            }
            
            QComboBox {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 6px;
            }
            
            QTableWidget, QTableView {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                gridline-color: #555555;
            }
            
            QHeaderView::section {
                background: #353535;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px;
                font-weight: bold;
            }
            
            QMenuBar {
                background: #353535;
                color: #ffffff;
                border-bottom: 1px solid #555555;
            }
            
            QMenuBar::item:selected {
                background: #505050;
            }
            
            QMenu {
                background: #404040;
                color: #ffffff;
                border: 1px solid #555555;
            }
            
            QMenu::item:selected {
                background: #3498db;
            }
            
            QStatusBar {
                background: #353535;
                color: #ffffff;
                border-top: 1px solid #555555;
            }
            
            QTabWidget::pane {
                background: #404040;
                border: 1px solid #555555;
            }
            
            QTabBar::tab {
                background: #353535;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px 16px;
                margin: 2px;
            }
            
            QTabBar::tab:selected {
                background: #3498db;
            }
            
            QScrollBar:vertical {
                background: #353535;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #777777;
            }
        """
        
        app = QApplication.instance()
        if app:
            app.setStyleSheet(dark_style)
            self.current_theme = "dark"
    
    def apply_light_theme(self):
        """تطبيق الثيم الفاتح"""
        return self.load_theme("modern")
    
    def reset_style(self):
        """إعادة تعيين الستايل للافتراضي"""
        app = QApplication.instance()
        if app:
            app.setStyleSheet("")
            self.current_theme = "default"

# إنشاء مثيل عام من مدير الستايلات
style_manager = StyleManager()
