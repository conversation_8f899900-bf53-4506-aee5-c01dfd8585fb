# دليل استيراد الموردين من الإكسيل

## 📋 نظرة عامة
تم إضافة ميزة استيراد الموردين من ملفات الإكسيل إلى نظام إدارة الموردين. هذه الميزة تسمح بإضافة عدد كبير من الموردين بسرعة وسهولة.

## 🚀 كيفية الاستخدام

### 1. الوصول إلى ميزة الاستيراد
- افتح نافذة إدارة الموردين
- انتقل إلى تبويب "بيانات الموردين"
- اضغط على زر **"📥 استيراد من إكسيل"** (باللون البرتقالي)

### 2. اختيار خيارات الاستيراد
ستظهر نافذة خيارات الاستيراد مع خيارين:

#### أ) تحديث الموردين الموجودين (افتراضي)
- إذا كان المورد موجود بالفعل، سيتم تحديث بياناته
- مفيد لتحديث معلومات الموردين الحاليين

#### ب) تجاهل الموردين الموجودين
- إذا كان المورد موجود، سيتم تجاهله
- مفيد لإضافة موردين جدد فقط

### 3. اختيار ملف الإكسيل
- اضغط "موافق" لاختيار الملف
- حدد ملف الإكسيل (.xlsx أو .xls)
- اضغط "فتح"

### 4. مراجعة النتائج
- ستظهر رسالة تحتوي على:
  - عدد الموردين المستوردين بنجاح
  - عدد الموردين الفاشلين
  - تفاصيل الأخطاء (إن وجدت)

## 📊 تنسيق ملف الإكسيل

### الأعمدة المطلوبة
| العمود | مطلوب | الوصف |
|---------|--------|-------|
| رقم المورد | ✅ نعم | رقم فريد للمورد (مثل: SUP001) |
| اسم المورد | ✅ نعم | اسم المورد باللغة العربية |

### الأعمدة الاختيارية
| العمود | الوصف | مثال |
|---------|--------|-------|
| الاسم الإنجليزي | اسم المورد بالإنجليزية | ABC Trading Company |
| نوع المورد | نوع المورد | شركة، مؤسسة، مكتب |
| الرقم الضريبي | الرقم الضريبي | ********* |
| السجل التجاري | رقم السجل التجاري | CR-2023-001 |
| الهاتف | رقم الهاتف | 011-2345678 |
| الجوال | رقم الجوال | 0501234567 |
| البريد الإلكتروني | عنوان البريد الإلكتروني | <EMAIL> |
| الموقع الإلكتروني | عنوان الموقع | www.company.com |
| الدولة | اسم الدولة | السعودية |
| المدينة | اسم المدينة | الرياض |
| العنوان | العنوان التفصيلي | شارع الملك فهد، حي العليا |
| الرمز البريدي | الرمز البريدي | 11564 |
| حد الائتمان | حد الائتمان بالريال | 50000.00 |
| مدة السداد | مدة السداد بالأيام | 30 |
| شخص الاتصال | اسم شخص الاتصال | أحمد محمد |

## 📁 ملفات نموذجية

### 1. ملف شامل
- **اسم الملف:** `نموذج_استيراد_الموردين.xlsx`
- **المحتوى:** 10 موردين مع جميع البيانات
- **الاستخدام:** للاختبار الشامل

### 2. ملف اختبار
- **اسم الملف:** `اختبار_استيراد_موردين.xlsx`
- **المحتوى:** 3 موردين للاختبار السريع
- **الاستخدام:** للاختبار السريع

## ⚠️ ملاحظات مهمة

### 1. التحقق من البيانات
- **اسم المورد مطلوب:** لا يمكن أن يكون فارغاً
- **عدم التكرار:** لا يمكن تكرار اسم المورد في نفس الملف
- **البريد الإلكتروني:** يجب أن يحتوي على @ إذا تم إدخاله

### 2. توليد الأكواد
- **كود تلقائي:** يتم توليد كود المورد تلقائياً (SUP0001, SUP0002, ...)
- **عدم التكرار:** النظام يتأكد من عدم تكرار الأكواد

### 3. معالجة الأخطاء
- **أخطاء الصفوف:** يتم تسجيل أخطاء كل صف منفصلاً
- **استمرار المعالجة:** الأخطاء في صف واحد لا توقف معالجة باقي الصفوف
- **تفاصيل الأخطاء:** يتم عرض تفاصيل الأخطاء مع أرقام الصفوف

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. "الأعمدة المطلوبة مفقودة"
- **السبب:** عمود "رقم المورد" أو "اسم المورد" غير موجود
- **الحل:** تأكد من وجود عمودين بعنوان "رقم المورد" و "اسم المورد" بالضبط

#### 2. "رقم المورد مطلوب"
- **السبب:** خلية رقم المورد فارغة
- **الحل:** املأ جميع خلايا رقم المورد بأرقام فريدة

#### 3. "اسم المورد مطلوب"
- **السبب:** خلية اسم المورد فارغة
- **الحل:** املأ جميع خلايا اسم المورد

#### 4. "رقم المورد مكرر في الملف"
- **السبب:** نفس رقم المورد موجود في أكثر من صف
- **الحل:** تأكد من أن كل مورد له رقم فريد

#### 4. "المورد موجود بالفعل"
- **السبب:** المورد موجود في قاعدة البيانات
- **الحل:** اختر "تحديث الموردين الموجودين" أو غير اسم المورد

## 📈 نصائح للاستخدام الأمثل

1. **ابدأ بملف صغير:** اختبر بـ 2-3 موردين أولاً
2. **تحقق من البيانات:** راجع البيانات قبل الاستيراد
3. **احتفظ بنسخة احتياطية:** احفظ نسخة من قاعدة البيانات قبل الاستيراد الكبير
4. **استخدم الملفات النموذجية:** ابدأ بالملفات النموذجية المرفقة
5. **راجع النتائج:** تحقق من النتائج بعد كل استيراد

## 🎯 الخلاصة
ميزة استيراد الموردين تسهل إدارة قاعدة بيانات الموردين وتوفر الوقت والجهد. استخدم الملفات النموذجية للبدء واتبع الإرشادات لضمان نجاح عملية الاستيراد.
