# -*- coding: utf-8 -*-
"""
نافذة إدارة الشحنات
Shipments Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QMessageBox, QHeaderView, QAbstractItemView, QComboBox,
                               QGroupBox, QFormLayout, QDateEdit, QToolBar, QStatusBar)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QAction, QIcon

from ...database.database_manager import DatabaseManager
from ...database.models import Shipment, Supplier
from .new_shipment_window import NewShipmentWindow
from .shipment_tracking_window import ShipmentTrackingWindow

class ShipmentsWindow(QMainWindow):
    """نافذة إدارة الشحنات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.setup_toolbar()
        self.setup_connections()
        self.load_shipments()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة الشحنات")
        self.resize(1200, 800)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # منطقة البحث والفلترة
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QFormLayout(search_group)
        
        # البحث بالنص
        search_text_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث برقم الشحنة أو المورد...")
        self.search_button = QPushButton("بحث")
        self.clear_search_button = QPushButton("مسح")
        
        search_text_layout.addWidget(self.search_edit)
        search_text_layout.addWidget(self.search_button)
        search_text_layout.addWidget(self.clear_search_button)
        search_layout.addRow("البحث:", search_text_layout)
        
        # فلترة بحالة الشحنة
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItem("جميع الحالات", "")
        self.status_filter_combo.addItems([
            "تحت الطلب", "مؤكدة", "تم الشحن", "في الطريق", 
            "وصلت الميناء", "في الجمارك", "تم التسليم", "ملغية", "متاخرة"
        ])
        search_layout.addRow("حالة الشحنة:", self.status_filter_combo)
        
        # فلترة بحالة الإفراج
        self.clearance_filter_combo = QComboBox()
        self.clearance_filter_combo.addItem("جميع حالات الإفراج", "")
        self.clearance_filter_combo.addItems(["بدون الافراج", "مع الافراج"])
        search_layout.addRow("حالة الإفراج:", self.clearance_filter_combo)
        
        main_layout.addWidget(search_group)
        
        # جدول الشحنات
        self.shipments_table = QTableWidget()
        self.shipments_table.setColumnCount(8)
        self.shipments_table.setHorizontalHeaderLabels([
            "رقم الشحنة", "المورد", "رقم فاتورة المورد", "حالة الشحنة", 
            "حالة الإفراج", "تاريخ الإنشاء", "المبلغ الإجمالي", "العملة"
        ])
        
        # إعداد الجدول
        header = self.shipments_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الشحنة
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # المورد
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # رقم الفاتورة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # حالة الشحنة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # حالة الإفراج
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # تاريخ الإنشاء
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # العملة
        
        self.shipments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.shipments_table.setAlternatingRowColors(True)
        
        main_layout.addWidget(self.shipments_table)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar("أدوات الشحنات")
        self.addToolBar(toolbar)
        
        # شحنة جديدة
        new_action = QAction("شحنة جديدة", self)
        new_action.setStatusTip("إنشاء شحنة جديدة")
        new_action.triggered.connect(self.new_shipment)
        toolbar.addAction(new_action)
        
        toolbar.addSeparator()
        
        # تتبع الشحنات
        track_action = QAction("تتبع الشحنات", self)
        track_action.setStatusTip("فتح نافذة تتبع الشحنات")
        track_action.triggered.connect(self.open_tracking_window)
        toolbar.addAction(track_action)
        
        toolbar.addSeparator()
        
        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setStatusTip("تحديث قائمة الشحنات")
        refresh_action.triggered.connect(self.load_shipments)
        toolbar.addAction(refresh_action)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_button.clicked.connect(self.search_shipments)
        self.clear_search_button.clicked.connect(self.clear_search)
        self.search_edit.returnPressed.connect(self.search_shipments)
        
        # فلترة عند تغيير الكومبو بوكس
        self.status_filter_combo.currentTextChanged.connect(self.filter_shipments)
        self.clearance_filter_combo.currentTextChanged.connect(self.filter_shipments)
        
        # النقر المزدوج لفتح الشحنة
        self.shipments_table.itemDoubleClicked.connect(self.edit_shipment)
        
    def load_shipments(self):
        """تحميل الشحنات"""
        session = self.db_manager.get_session()
        try:
            shipments = session.query(Shipment).join(Supplier).filter(
                Shipment.is_active == True
            ).order_by(Shipment.created_at.desc()).all()
            
            self.populate_table(shipments)
            self.status_bar.showMessage(f"تم تحميل {len(shipments)} شحنة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الشحنات: {str(e)}")
        finally:
            session.close()
            
    def populate_table(self, shipments):
        """ملء الجدول بالشحنات"""
        self.shipments_table.setRowCount(len(shipments))
        
        for row, shipment in enumerate(shipments):
            # رقم الشحنة
            number_item = QTableWidgetItem(shipment.shipment_number or "")
            number_item.setData(Qt.UserRole, shipment.id)
            self.shipments_table.setItem(row, 0, number_item)
            
            # المورد
            supplier_name = shipment.supplier.name if shipment.supplier else ""
            supplier_item = QTableWidgetItem(supplier_name)
            self.shipments_table.setItem(row, 1, supplier_item)
            
            # رقم فاتورة المورد
            invoice_item = QTableWidgetItem(shipment.supplier_invoice_number or "")
            self.shipments_table.setItem(row, 2, invoice_item)
            
            # حالة الشحنة
            status_item = QTableWidgetItem(shipment.shipment_status or "")
            self.shipments_table.setItem(row, 3, status_item)
            
            # حالة الإفراج
            clearance_item = QTableWidgetItem(shipment.clearance_status or "")
            self.shipments_table.setItem(row, 4, clearance_item)
            
            # تاريخ الإنشاء
            created_date = shipment.created_at.strftime("%Y-%m-%d") if shipment.created_at else ""
            date_item = QTableWidgetItem(created_date)
            self.shipments_table.setItem(row, 5, date_item)
            
            # المبلغ الإجمالي
            amount_item = QTableWidgetItem(f"{shipment.total_amount:.2f}" if shipment.total_amount else "0.00")
            self.shipments_table.setItem(row, 6, amount_item)
            
            # العملة
            currency_name = shipment.currency.code if shipment.currency else ""
            currency_item = QTableWidgetItem(currency_name)
            self.shipments_table.setItem(row, 7, currency_item)
            
    def search_shipments(self):
        """البحث في الشحنات"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.load_shipments()
            return
            
        session = self.db_manager.get_session()
        try:
            shipments = session.query(Shipment).join(Supplier).filter(
                Shipment.is_active == True,
                (Shipment.shipment_number.contains(search_text) |
                 Supplier.name.contains(search_text) |
                 Supplier.code.contains(search_text))
            ).order_by(Shipment.created_at.desc()).all()
            
            self.populate_table(shipments)
            self.status_bar.showMessage(f"تم العثور على {len(shipments)} شحنة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث: {str(e)}")
        finally:
            session.close()
            
    def filter_shipments(self):
        """فلترة الشحنات"""
        status_filter = self.status_filter_combo.currentData() or self.status_filter_combo.currentText()
        clearance_filter = self.clearance_filter_combo.currentData() or self.clearance_filter_combo.currentText()
        
        session = self.db_manager.get_session()
        try:
            query = session.query(Shipment).join(Supplier).filter(Shipment.is_active == True)
            
            if status_filter and status_filter != "جميع الحالات":
                query = query.filter(Shipment.shipment_status == status_filter)
                
            if clearance_filter and clearance_filter != "جميع حالات الإفراج":
                query = query.filter(Shipment.clearance_status == clearance_filter)
                
            shipments = query.order_by(Shipment.created_at.desc()).all()
            self.populate_table(shipments)
            self.status_bar.showMessage(f"تم فلترة {len(shipments)} شحنة")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في الفلترة: {str(e)}")
        finally:
            session.close()
            
    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.status_filter_combo.setCurrentIndex(0)
        self.clearance_filter_combo.setCurrentIndex(0)
        self.load_shipments()
        
    def new_shipment(self):
        """إنشاء شحنة جديدة"""
        dialog = NewShipmentWindow(self)
        dialog.shipment_saved.connect(self.load_shipments)
        dialog.exec()
        
    def edit_shipment(self):
        """تعديل الشحنة المحددة"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0:
            shipment_id = self.shipments_table.item(current_row, 0).data(Qt.UserRole)
            # TODO: إنشاء نافذة تعديل الشحنة
            QMessageBox.information(self, "معلومات", f"سيتم فتح نافذة تعديل الشحنة رقم: {shipment_id}")
        else:
            QMessageBox.warning(self, "تحذير", "يجب اختيار شحنة أولاً")
            
    def open_tracking_window(self):
        """فتح نافذة تتبع الشحنات"""
        tracking_window = ShipmentTrackingWindow(self)
        tracking_window.show()
