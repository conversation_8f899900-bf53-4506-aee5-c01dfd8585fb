#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QComboBox, QPushButton, QGroupBox, QGridLayout,
                               QScrollArea, QWidget, QMessageBox, QFrame)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class ExcelImportDialog(QDialog):
    """نافذة حوار لاختيار تطابق الأعمدة من Excel"""
    
    def __init__(self, parent=None, dataframe=None):
        super().__init__(parent)
        self.df = dataframe
        self.column_mapping = {}
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("استيراد البيانات من Excel")
        self.setModal(True)
        self.resize(800, 600)
        
        # تطبيق الاتجاه من اليمين لليسار
        self.setLayoutDirection(Qt.RightToLeft)
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("اختر الأعمدة المطابقة للحقول")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                border: 2px solid #3498db;
            }
        """)
        main_layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
        """)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # إنشاء مجموعات الحقول
        self.create_basic_data_fields_group(scroll_layout)
        self.create_shipping_fields_group(scroll_layout)
        self.create_container_fields_group(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)
        
        # الأزرار
        self.create_buttons(main_layout)
        
    def create_basic_data_fields_group(self, parent_layout):
        """إنشاء مجموعة حقول البيانات الأساسية"""
        group = QGroupBox("البيانات الأساسية")
        group.setStyleSheet(self.get_group_style())

        layout = QGridLayout(group)
        layout.setSpacing(10)

        # قائمة حقول البيانات الأساسية
        basic_fields = [
            ("shipment_date", "التاريخ"),
            ("shipment_number", "رقم الشحنة"),
            ("supplier", "المورد"),
            ("supplier_invoice", "فاتورة المورد"),
            ("shipment_status", "حالة الشحنة"),
            ("clearance_status", "حالة الإفراج"),
            ("tracking_number", "رقم التتبع"),
            ("bill_of_lading", "بوليصة الشحن"),
            ("notes", "ملاحظات")
        ]

        for i, (field_key, field_label) in enumerate(basic_fields):
            label = QLabel(f"{field_label}:")
            combo = self.create_column_combo()

            layout.addWidget(label, i, 0)
            layout.addWidget(combo, i, 1)

            # حفظ المرجع
            setattr(self, f"{field_key}_combo", combo)

        parent_layout.addWidget(group)
    
    def create_shipping_fields_group(self, parent_layout):
        """إنشاء مجموعة حقول بيانات الشحن"""
        group = QGroupBox("بيانات الشحن")
        group.setStyleSheet(self.get_group_style())
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # قائمة حقول الشحن
        shipping_fields = [
            ("shipping_company", "شركة الشحن"),
            ("arrival_port", "ميناء الوصول"),
            ("dhl_number", "رقم DHL"),
            ("expected_arrival_date", "تاريخ الوصول المتوقع")
        ]
        
        for i, (field_key, field_label) in enumerate(shipping_fields):
            label = QLabel(f"{field_label}:")
            combo = self.create_column_combo()
            
            layout.addWidget(label, i, 0)
            layout.addWidget(combo, i, 1)
            
            # حفظ المرجع
            setattr(self, f"{field_key}_combo", combo)
        
        parent_layout.addWidget(group)
    
    def create_container_fields_group(self, parent_layout):
        """إنشاء مجموعة حقول الحاويات"""
        group = QGroupBox("بيانات الحاويات")
        group.setStyleSheet(self.get_group_style())
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # حقل رقم الحاوية
        label = QLabel("رقم الحاوية:")
        combo = self.create_column_combo()
        
        layout.addWidget(label, 0, 0)
        layout.addWidget(combo, 0, 1)
        
        # حفظ المرجع
        setattr(self, "container_number_combo", combo)
        
        parent_layout.addWidget(group)
    
    def create_column_combo(self):
        """إنشاء قائمة منسدلة للأعمدة"""
        combo = QComboBox()
        combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
                min-height: 25px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
            }
        """)
        
        # إضافة خيار فارغ
        combo.addItem("-- لا يوجد --", "")
        
        # إضافة أعمدة Excel
        if self.df is not None:
            for column in self.df.columns:
                combo.addItem(str(column), str(column))
        
        return combo
    
    def create_buttons(self, parent_layout):
        """إنشاء الأزرار"""
        # فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("color: #bdc3c7;")
        parent_layout.addWidget(line)
        
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        # زر الاستيراد
        import_btn = QPushButton("📊 استيراد البيانات")
        import_btn.setFixedSize(150, 40)
        import_btn.setStyleSheet(self.get_button_style("#27ae60", "#229954"))
        import_btn.clicked.connect(self.accept)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFixedSize(120, 40)
        cancel_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(import_btn)
        button_layout.addWidget(cancel_btn)
        button_layout.addStretch()
        
        parent_layout.addLayout(button_layout)
    
    def get_group_style(self):
        """الحصول على نمط المجموعة"""
        return """
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                background-color: white;
            }
        """
    
    def get_button_style(self, color1, color2):
        """الحصول على نمط الزر"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color1}, stop:1 {color2});
                color: white;
                font-weight: bold;
                font-size: 11px;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color2}, stop:1 {color1});
            }}
            QPushButton:pressed {{
                background: {color2};
            }}
        """
    
    def get_column_mapping(self):
        """الحصول على تطابق الأعمدة"""
        mapping = {}

        # البيانات الأساسية
        basic_fields = ["shipment_date", "shipment_number", "supplier", "supplier_invoice",
                       "shipment_status", "clearance_status", "tracking_number",
                       "bill_of_lading", "notes"]

        for field in basic_fields:
            combo = getattr(self, f"{field}_combo", None)
            if combo:
                mapping[field] = combo.currentData()

        # بيانات الشحن
        shipping_fields = ["shipping_company", "arrival_port", "dhl_number", "expected_arrival_date"]

        for field in shipping_fields:
            combo = getattr(self, f"{field}_combo", None)
            if combo:
                mapping[field] = combo.currentData()

        # بيانات الحاويات
        container_combo = getattr(self, "container_number_combo", None)
        if container_combo:
            mapping["container_number"] = container_combo.currentData()

        return mapping
