#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from datetime import datetime, date

def create_sample_excel():
    """إنشاء ملف Excel تجريبي لاختبار الاستيراد"""

    # بيانات تجريبية للبيانات الأساسية
    data = {
        'التاريخ': [date(2024, 12, 10)],
        'رقم_الشحنة': ['SH-2024-12345'],
        'المورد': ['شركة التجارة العالمية المحدودة'],
        'فاتورة_المورد': ['INV-2024-5678'],
        'حالة_الشحنة': ['تم الشحن'],
        'حالة_الإفراج': ['مع الافراج'],
        'رقم_التتبع': ['TRK987654321'],
        'بوليصة_الشحن': ['BOL-2024-ABC123'],
        'ملاحظات': ['شحنة تجريبية تحتوي على مواد إلكترونية وقطع غيار'],
        'شركة_الشحن': ['شركة الشحن العالمية'],
        'ميناء_الوصول': ['ميناء جدة الإسلامي'],
        'رقم_DHL': ['DHL123456789'],
        'تاريخ_الوصول_المتوقع': [date(2024, 12, 15)],
        'رقم_الحاوية': ['CONT-2024-001']
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # حفظ الملف
    filename = 'sample_shipment_data.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel التجريبي: {filename}")
    print("📊 البيانات المتضمنة:")
    print(f"   • التاريخ: {data['التاريخ'][0]}")
    print(f"   • رقم الشحنة: {data['رقم_الشحنة'][0]}")
    print(f"   • المورد: {data['المورد'][0]}")
    print(f"   • فاتورة المورد: {data['فاتورة_المورد'][0]}")
    print(f"   • حالة الشحنة: {data['حالة_الشحنة'][0]}")
    print(f"   • حالة الإفراج: {data['حالة_الإفراج'][0]}")
    print(f"   • رقم التتبع: {data['رقم_التتبع'][0]}")
    print(f"   • بوليصة الشحن: {data['بوليصة_الشحن'][0]}")
    print(f"   • شركة الشحن: {data['شركة_الشحن'][0]}")
    print(f"   • ميناء الوصول: {data['ميناء_الوصول'][0]}")
    print(f"   • رقم DHL: {data['رقم_DHL'][0]}")
    print(f"   • رقم الحاوية: {data['رقم_الحاوية'][0]}")
    print(f"   • ملاحظات: {data['ملاحظات'][0][:50]}...")
    
    return filename

if __name__ == "__main__":
    try:
        create_sample_excel()
    except ImportError:
        print("❌ خطأ: مكتبات pandas و openpyxl غير مثبتة")
        print("💡 قم بتثبيتها باستخدام:")
        print("   pip install pandas openpyxl")
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
