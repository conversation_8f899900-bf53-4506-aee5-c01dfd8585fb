#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from datetime import datetime, date

def create_sample_excel():
    """إنشاء ملف Excel تجريبي لاختبار الاستيراد"""
    
    # بيانات تجريبية
    data = {
        'سعر_الصرف': [3.75],
        'قيمة_البضاعة': [15000.50],
        'تكلفة_الشحن': [2500.00],
        'تكلفة_التأمين': [750.25],
        'رسوم_الجمارك': [1200.00],
        'رسوم_أخرى': [300.75],
        'المبلغ_المدفوع': [12000.00],
        'شركة_الشحن': ['شركة الشحن العالمية'],
        'ميناء_الوصول': ['ميناء جدة الإسلامي'],
        'رقم_DHL': ['DHL123456789'],
        'تاريخ_الوصول_المتوقع': [date(2024, 12, 15)],
        'رقم_الحاوية': ['CONT-2024-001']
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # حفظ الملف
    filename = 'sample_shipment_data.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel التجريبي: {filename}")
    print("📊 البيانات المتضمنة:")
    print(f"   • سعر الصرف: {data['سعر_الصرف'][0]}")
    print(f"   • قيمة البضاعة: {data['قيمة_البضاعة'][0]}")
    print(f"   • تكلفة الشحن: {data['تكلفة_الشحن'][0]}")
    print(f"   • شركة الشحن: {data['شركة_الشحن'][0]}")
    print(f"   • ميناء الوصول: {data['ميناء_الوصول'][0]}")
    print(f"   • رقم DHL: {data['رقم_DHL'][0]}")
    print(f"   • رقم الحاوية: {data['رقم_الحاوية'][0]}")
    
    return filename

if __name__ == "__main__":
    try:
        create_sample_excel()
    except ImportError:
        print("❌ خطأ: مكتبات pandas و openpyxl غير مثبتة")
        print("💡 قم بتثبيتها باستخدام:")
        print("   pip install pandas openpyxl")
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
