#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة إدارة الموردين المحسنة
Test Enhanced Suppliers Management UI
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from src.ui.suppliers.suppliers_data import SuppliersDataWidget
from src.database.database_manager import DatabaseManager
from src.utils.arabic_support import setup_arabic_support

class TestSuppliersWindow(QMainWindow):
    """نافذة اختبار واجهة الموردين المحسنة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار واجهة إدارة الموردين المحسنة")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        # إعداد قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان الاختبار
        title_label = QLabel("🔧 اختبار التحسينات الجديدة - واجهة إدارة الموردين")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(20)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                padding: 20px;
                border-radius: 15px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # وصف التحسينات
        improvements_label = QLabel("""
        ✅ تم إصلاح مشكلة تداخل الحقول في تبويب بيانات الموردين
        ✅ تحسين التخطيط باستخدام VBoxLayout بدلاً من FormLayout المتداخل
        ✅ إضافة منطقة تمرير للنموذج لتجنب التداخل في الشاشات الصغيرة
        ✅ تحسين أحجام الحقول وإضافة ارتفاع ثابت للعناصر
        ✅ تحسين تنسيق الجدول مع أعمدة قابلة للتمدد
        ✅ إضافة أيقونات للأزرار وتحسين الألوان
        ✅ تحسين المسافات والهوامش لمظهر أكثر احترافية
        """)
        improvements_label.setAlignment(Qt.AlignRight)
        improvements_font = QFont()
        improvements_font.setPointSize(12)
        improvements_label.setFont(improvements_font)
        improvements_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 15px;
                color: #495057;
                line-height: 1.6;
            }
        """)
        main_layout.addWidget(improvements_label)
        
        # إضافة ويدجت بيانات الموردين المحسن
        self.suppliers_widget = SuppliersDataWidget()
        main_layout.addWidget(self.suppliers_widget)
        
        # تطبيق تنسيق عام للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
        """)

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد دعم اللغة العربية
    setup_arabic_support(app)
    
    # إنشاء النافذة الرئيسية
    window = TestSuppliersWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
