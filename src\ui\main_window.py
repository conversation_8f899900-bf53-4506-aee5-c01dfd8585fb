# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
Main Application Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QMenuBar, QMenu, QToolBar, QStatusBar,
                               QLabel, QPushButton, QFrame, QGridLayout, QMessageBox,
                               QScrollArea, QGraphicsDropShadowEffect, QSizePolicy)
from PySide6.QtCore import Qt, QSize, Signal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QIcon, QPixmap, QFont, QAction, QLinearGradient, QColor, QPainter, QPalette

from ..utils.arabic_support import reshape_arabic_text
from .settings.settings_window import SettingsWindow
from .items.items_window import ItemsWindow
from .suppliers.suppliers_window import SuppliersWindow
from .shipments.shipments_window import ShipmentsWindow
from .styles.style_manager import style_manager

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات المتكامل - ProShipment")
        self.setMinimumSize(1200, 800)

        # تعيين النافذة لتفتح في وضع ملء الشاشة
        self.showMaximized()

        # تحميل الثيم الحديث
        style_manager.load_theme("modern")

        # إعداد الستايل العام
        self.setup_global_style()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()

        # إعداد الرسوم المتحركة
        self.setup_animations()

        # متغيرات النوافذ المنفصلة
        self.settings_window = None
        self.items_window = None
        self.suppliers_window = None
        self.shipments_window = None

    def setup_global_style(self):
        """إعداد الستايل العام للتطبيق"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:0.5 #e9ecef, stop:1 #dee2e6);
            }

            QMenuBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                color: white;
                border: none;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
            }

            QMenuBar::item {
                background: transparent;
                padding: 8px 15px;
                border-radius: 4px;
                margin: 2px;
            }

            QMenuBar::item:selected {
                background: rgba(255, 255, 255, 0.2);
            }

            QMenu {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 5px;
                font-size: 11px;
            }

            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 1px;
            }

            QMenu::item:selected {
                background: #3498db;
                color: white;
            }

            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                border: none;
                spacing: 5px;
                padding: 5px;
            }

            QToolBar QToolButton {
                background: transparent;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 11px;
                font-weight: bold;
            }

            QToolBar QToolButton:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                border-top: 1px solid #95a5a6;
                color: #2c3e50;
                font-size: 11px;
            }
        """)

    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        # مؤقت لتحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # تحديث كل ثانية

    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        # الويدجت المركزي مع منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #ecf0f1;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #3498db;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #2980b9;
            }
        """)

        central_widget = QWidget()
        scroll_area.setWidget(central_widget)
        self.setCentralWidget(scroll_area)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(30)
        main_layout.setContentsMargins(30, 30, 30, 30)

        # إضافة الهيدر الجديد
        self.create_header(main_layout)

        # إضافة قسم الإحصائيات السريعة
        self.create_stats_section(main_layout)

        # شبكة الأزرار الرئيسية المحسنة
        self.create_main_buttons_section(main_layout)

        # إضافة قسم الأخبار والتحديثات
        self.create_news_section(main_layout)

        main_layout.addStretch()

    def create_header(self, layout):
        """إنشاء الهيدر الرئيسي"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                border-radius: 20px;
                padding: 20px;
            }
        """)

        # إضافة تأثير الظل (معطل مؤقتاً لتجنب أخطاء QPainter)
        # shadow = QGraphicsDropShadowEffect()
        # shadow.setBlurRadius(20)
        # shadow.setColor(QColor(0, 0, 0, 60))
        # shadow.setOffset(0, 5)
        # header_frame.setGraphicsEffect(shadow)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(10)

        # العنوان الرئيسي
        title_label = QLabel("🚢 نظام إدارة الشحنات المتكامل")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(28)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: transparent;
                padding: 10px;
            }
        """)
        header_layout.addWidget(title_label)

        # العنوان الفرعي
        subtitle_label = QLabel("إدارة شاملة ومتطورة لجميع عمليات الشحن والتوريد")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(14)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                background: transparent;
                padding: 5px;
            }
        """)
        header_layout.addWidget(subtitle_label)

        layout.addWidget(header_frame)

    def create_stats_section(self, layout):
        """إنشاء قسم الإحصائيات السريعة"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
            }
        """)

        # إضافة تأثير الظل (معطل مؤقتاً)
        # shadow = QGraphicsDropShadowEffect()
        # shadow.setBlurRadius(15)
        # shadow.setColor(QColor(0, 0, 0, 30))
        # shadow.setOffset(0, 3)
        # stats_frame.setGraphicsEffect(shadow)

        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(20)
        stats_layout.setContentsMargins(25, 20, 25, 20)

        # بيانات الإحصائيات
        stats_data = [
            ("📦", "إجمالي الشحنات", "1,234", "#3498db"),
            ("🏭", "الموردين النشطين", "89", "#2ecc71"),
            ("📋", "الأصناف المسجلة", "5,678", "#e74c3c"),
            ("⏱️", "الشحنات المعلقة", "23", "#f39c12")
        ]

        for icon, title, value, color in stats_data:
            stat_widget = self.create_stat_widget(icon, title, value, color)
            stats_layout.addWidget(stat_widget)

        layout.addWidget(stats_frame)

    def create_stat_widget(self, icon, title, value, color):
        """إنشاء ويدجت إحصائية واحدة"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 12px;
                padding: 15px;
            }}
            QFrame:hover {{
                background: {self.darken_color(color)};
            }}
        """)

        layout = QVBoxLayout(widget)
        layout.setSpacing(8)
        layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(24)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("color: white; background: transparent;")
        layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_font = QFont()
        value_font.setPointSize(20)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet("color: white; background: transparent;")
        layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(10)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); background: transparent;")
        layout.addWidget(title_label)

        return widget

    def darken_color(self, color):
        """تغميق اللون للتأثير عند التمرير"""
        color_map = {
            "#3498db": "#2980b9",
            "#2ecc71": "#27ae60",
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22"
        }
        return color_map.get(color, color)

    def create_main_buttons_section(self, layout):
        """إنشاء قسم الأزرار الرئيسية المحسن"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
                padding: 20px;
            }
        """)

        # إضافة تأثير الظل (معطل مؤقتاً)
        # shadow = QGraphicsDropShadowEffect()
        # shadow.setBlurRadius(15)
        # shadow.setColor(QColor(0, 0, 0, 30))
        # shadow.setOffset(0, 3)
        # buttons_frame.setGraphicsEffect(shadow)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(20)

        # عنوان القسم
        section_title = QLabel("🎛️ الأنظمة الرئيسية")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        section_title.setFont(title_font)
        section_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                padding: 10px;
            }
        """)
        buttons_layout.addWidget(section_title)

        # شبكة الأزرار
        grid_layout = QGridLayout()
        grid_layout.setSpacing(20)

        # إنشاء أزرار الأنظمة الرئيسية
        self.create_system_buttons(grid_layout)

        buttons_layout.addLayout(grid_layout)
        layout.addWidget(buttons_frame)

    def create_news_section(self, layout):
        """إنشاء قسم الأخبار والتحديثات"""
        news_frame = QFrame()
        news_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
                padding: 20px;
            }
        """)

        # إضافة تأثير الظل (معطل مؤقتاً)
        # shadow = QGraphicsDropShadowEffect()
        # shadow.setBlurRadius(15)
        # shadow.setColor(QColor(0, 0, 0, 30))
        # shadow.setOffset(0, 3)
        # news_frame.setGraphicsEffect(shadow)

        news_layout = QVBoxLayout(news_frame)
        news_layout.setSpacing(15)

        # عنوان القسم
        section_title = QLabel("📢 آخر التحديثات")
        section_title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        section_title.setFont(title_font)
        section_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                padding: 10px;
            }
        """)
        news_layout.addWidget(section_title)

        # قائمة الأخبار
        news_items = [
            "✅ تم إضافة نظام إدارة المرفقات للمستندات",
            "🔄 تحسين واجهة إدارة الشحنات",
            "📊 إضافة تقارير جديدة للموردين",
            "🔒 تحسينات أمنية في النظام"
        ]

        for news in news_items:
            news_label = QLabel(news)
            news_label.setStyleSheet("""
                QLabel {
                    color: #34495e;
                    background: #f8f9fa;
                    padding: 12px;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                    font-size: 12px;
                }
            """)
            news_layout.addWidget(news_label)

        layout.addWidget(news_frame)

    def create_system_buttons(self, layout):
        """إنشاء أزرار الأنظمة الرئيسية"""

        # بيانات الأزرار مع الأيقونات والألوان
        buttons_data = [
            ("⚙️", "الإعدادات العامة", "إدارة إعدادات النظام والشركة والمستخدمين", self.open_settings, 0, 0, "#9b59b6"),
            ("📦", "إدارة الأصناف", "إدارة الأصناف ووحدات القياس والمجموعات", self.open_items, 0, 1, "#3498db"),
            ("🏭", "إدارة الموردين", "إدارة بيانات الموردين والعمليات والتقارير", self.open_suppliers, 0, 2, "#2ecc71"),
            ("🚢", "إدارة الشحنات", "نظام إدارة وتتبع الشحنات المتكامل", self.open_shipments, 1, 0, "#e74c3c"),
            ("🏛️", "الإدخالات الجمركية", "نظام الإدخالات الجمركية (قيد التطوير)", self.show_under_development, 1, 1, "#f39c12"),
            ("💰", "إدارة التكاليف", "نظام إدارة التكاليف (قيد التطوير)", self.show_under_development, 1, 2, "#1abc9c"),
        ]

        for icon, text, tooltip, callback, row, col, color in buttons_data:
            button = self.create_main_button(icon, text, tooltip, callback, color)
            layout.addWidget(button, row, col)
    
    def create_main_button(self, icon, text, tooltip, callback, color):
        """إنشاء زر رئيسي محسن"""
        button = QPushButton()
        button.setToolTip(tooltip)
        button.setMinimumSize(280, 120)
        button.clicked.connect(callback)
        button.setCursor(Qt.PointingHandCursor)

        # إنشاء التخطيط الداخلي للزر
        button_layout = QVBoxLayout()
        button_layout.setSpacing(8)
        button_layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_font = QFont()
        icon_font.setPointSize(28)
        icon_label.setFont(icon_font)
        icon_label.setStyleSheet("background: transparent; color: white;")

        # النص
        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        text_font = QFont()
        text_font.setPointSize(12)
        text_font.setBold(True)
        text_label.setFont(text_font)
        text_label.setStyleSheet("background: transparent; color: white;")

        # إضافة العناصر للتخطيط
        button_layout.addWidget(icon_label)
        button_layout.addWidget(text_label)

        # إنشاء ويدجت داخلي
        inner_widget = QWidget()
        inner_widget.setLayout(button_layout)

        # تخطيط الزر الرئيسي
        main_layout = QVBoxLayout(button)
        main_layout.addWidget(inner_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # تنسيق الزر مع التدرج والظلال
        hover_color = self.darken_color(color)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {hover_color});
                border: none;
                border-radius: 15px;
                padding: 20px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {hover_color}, stop:1 {color});
            }}
            QPushButton:pressed {{
                background: {hover_color};
            }}
        """)

        # إضافة تأثير الظل (معطل مؤقتاً)
        # shadow = QGraphicsDropShadowEffect()
        # shadow.setBlurRadius(20)
        # shadow.setColor(QColor(0, 0, 0, 40))
        # shadow.setOffset(0, 5)
        # button.setGraphicsEffect(shadow)

        return button
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        # إجراءات قائمة ملف
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الأنظمة
        systems_menu = menubar.addMenu("الأنظمة")
        
        settings_action = QAction("الإعدادات العامة", self)
        settings_action.triggered.connect(self.open_settings)
        systems_menu.addAction(settings_action)
        
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(self.open_items)
        systems_menu.addAction(items_action)
        
        suppliers_action = QAction("إدارة الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        systems_menu.addAction(suppliers_action)

        shipments_action = QAction("إدارة الشحنات", self)
        shipments_action.triggered.connect(self.open_shipments)
        systems_menu.addAction(shipments_action)
        
        # قائمة العرض والثيمات
        view_menu = menubar.addMenu("العرض")

        # قائمة فرعية للثيمات
        themes_menu = view_menu.addMenu("الثيمات")

        modern_theme_action = QAction("الثيم الحديث", self)
        modern_theme_action.triggered.connect(lambda: style_manager.load_theme("modern"))
        themes_menu.addAction(modern_theme_action)

        dark_theme_action = QAction("الثيم المظلم", self)
        dark_theme_action.triggered.connect(style_manager.apply_dark_theme)
        themes_menu.addAction(dark_theme_action)

        light_theme_action = QAction("الثيم الفاتح", self)
        light_theme_action.triggered.connect(style_manager.apply_light_theme)
        themes_menu.addAction(light_theme_action)

        themes_menu.addSeparator()

        reset_theme_action = QAction("إعادة تعيين الثيم", self)
        reset_theme_action.triggered.connect(style_manager.reset_style)
        themes_menu.addAction(reset_theme_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")

        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # أزرار شريط الأدوات
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)
        
        items_action = QAction("الأصناف", self)
        items_action.triggered.connect(self.open_items)
        toolbar.addAction(items_action)
        
        suppliers_action = QAction("الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        toolbar.addAction(suppliers_action)

        shipments_action = QAction("الشحنات", self)
        shipments_action.triggered.connect(self.open_shipments)
        toolbar.addAction(shipments_action)
        
        toolbar.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        toolbar.addAction(backup_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة المحسن"""
        status_bar = self.statusBar()

        # معلومات الحالة مع أيقونة
        self.status_label = QLabel("🟢 النظام جاهز")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addWidget(self.status_label)

        # معلومات المستخدم مع أيقونة
        user_label = QLabel("👤 المستخدم: مدير النظام")
        user_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addPermanentWidget(user_label)

        # التاريخ والوقت مع أيقونة
        from datetime import datetime
        self.date_label = QLabel(f"📅 {datetime.now().strftime('%Y/%m/%d - %H:%M:%S')}")
        self.date_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        status_bar.addPermanentWidget(self.date_label)

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        from datetime import datetime
        current_time = datetime.now().strftime('%Y/%m/%d - %H:%M:%S')
        self.date_label.setText(f"📅 {current_time}")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        if self.settings_window is None or not self.settings_window.isVisible():
            self.settings_window = SettingsWindow(self)
        
        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()
    
    def open_items(self):
        """فتح نافذة إدارة الأصناف"""
        if self.items_window is None or not self.items_window.isVisible():
            self.items_window = ItemsWindow(self)
        
        self.items_window.show()
        self.items_window.raise_()
        self.items_window.activateWindow()
    
    def open_suppliers(self):
        """فتح نافذة إدارة الموردين"""
        if self.suppliers_window is None or not self.suppliers_window.isVisible():
            self.suppliers_window = SuppliersWindow(self)
        
        self.suppliers_window.show()
        self.suppliers_window.raise_()
        self.suppliers_window.activateWindow()

    def open_shipments(self):
        """فتح نافذة إدارة الشحنات"""
        if self.shipments_window is None or not self.shipments_window.isVisible():
            self.shipments_window = ShipmentsWindow(self)

        self.shipments_window.show()
        self.shipments_window.raise_()
        self.shipments_window.activateWindow()
    
    def show_under_development(self):
        """عرض رسالة قيد التطوير"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "هذه الميزة قيد التطوير وستكون متاحة في الإصدارات القادمة."
        )
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        from ..database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.backup_database():
            QMessageBox.information(
                self,
                "نسخة احتياطية",
                "تم إنشاء النسخة الاحتياطية بنجاح"
            )
        else:
            QMessageBox.warning(
                self,
                "خطأ",
                "فشل في إنشاء النسخة الاحتياطية"
            )
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            """
            <h3>نظام إدارة الشحنات المتكامل</h3>
            <p><b>الإصدار:</b> 1.0.0</p>
            <p><b>الوصف:</b> نظام شامل لإدارة الشحنات والموردين والأصناف</p>
            <p><b>المطور:</b> فريق ProShipment</p>
            <p><b>حقوق النشر:</b> © 2024 جميع الحقوق محفوظة</p>
            """
        )
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الخروج",
            "هل تريد إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إغلاق النوافذ المنفصلة
            if self.settings_window:
                self.settings_window.close()
            if self.items_window:
                self.items_window.close()
            if self.suppliers_window:
                self.suppliers_window.close()
            if self.shipments_window:
                self.shipments_window.close()
            
            event.accept()
        else:
            event.ignore()
